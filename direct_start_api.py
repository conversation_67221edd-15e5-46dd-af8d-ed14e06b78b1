#!/usr/bin/env python3
"""
中国信访系统API服务直接启动脚本
"""
import os
import sys
import subprocess
import time

def main():
    print("🚀 启动中国信访系统API服务")
    print("=" * 60)
    
    # 设置工作目录
    api_dir = "/Users/<USER>/Desktop/code/xinfang/api-server"
    os.chdir(api_dir)
    
    print(f"📍 工作目录: {os.getcwd()}")
    print(f"🐍 Python版本: {sys.version}")
    
    # 显示服务信息
    print("\n🌐 服务将在以下地址启动:")
    print("   主页: http://localhost:8000")
    print("   API基础: http://localhost:8000/api/v1")
    print("\n📚 API文档地址:")
    print("   Swagger UI: http://localhost:8000/docs")
    print("   ReDoc: http://localhost:8000/redoc")
    print("   OpenAPI JSON: http://localhost:8000/openapi.json")
    
    print("\n🎯 主要API端点:")
    print("   POST /api/v1/auth/login - 用户登录")
    print("   GET  /api/v1/visiting - 来访接待列表")
    print("   GET  /api/v1/online - 网上信访列表")
    print("   GET  /api/v1/letter/letters - 信件处理列表")
    
    print("\n" + "=" * 60)
    print("🌟 正在启动服务... (按 Ctrl+C 停止)")
    print("=" * 60)
    
    try:
        # 运行main.py
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n💡 手动启动步骤:")
        print("   1. cd /Users/<USER>/Desktop/code/xinfang/api-server")
        print("   2. source venv/bin/activate")
        print("   3. python main.py")

if __name__ == "__main__":
    main()
