#!/usr/bin/env python3
"""
检查API服务器配置和启动环境
"""
import os
import sys
from pathlib import Path

def check_files():
    """检查必要文件"""
    print("🔍 检查API服务器文件...")
    
    base_path = "/Users/<USER>/Desktop/code/xinfang/api-server"
    required_files = [
        "main.py",
        "requirements.txt", 
        "app/__init__.py",
        "app/core/config.py",
        "app/api/__init__.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = os.path.join(base_path, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 不存在")
            all_exist = False
    
    return all_exist

def check_venv():
    """检查虚拟环境"""
    print("\n🐍 检查虚拟环境...")
    
    venv_path = "/Users/<USER>/Desktop/code/xinfang/api-server/venv"
    if os.path.exists(venv_path):
        print("✅ 虚拟环境存在")
        
        # 检查Python可执行文件
        python_exe = os.path.join(venv_path, "bin", "python")
        if os.path.exists(python_exe):
            print("✅ Python可执行文件存在")
            return True
        else:
            print("❌ Python可执行文件不存在")
            return False
    else:
        print("❌ 虚拟环境不存在")
        return False

def show_startup_commands():
    """显示启动命令"""
    print("\n🚀 API服务启动命令:")
    print("=" * 50)
    print("方法1 - 手动启动:")
    print("  cd /Users/<USER>/Desktop/code/xinfang/api-server")
    print("  source venv/bin/activate")
    print("  python main.py")
    print()
    print("方法2 - 使用脚本:")
    print("  cd /Users/<USER>/Desktop/code/xinfang")
    print("  python quick_start_api.py")
    print()
    print("📄 API文档地址:")
    print("  Swagger UI: http://localhost:8000/docs")
    print("  ReDoc:      http://localhost:8000/redoc")
    print("  API Base:   http://localhost:8000/api/v1")

def main():
    print("🏛️ 中国信访系统API服务器环境检查")
    print("=" * 60)
    
    # 检查文件
    files_ok = check_files()
    
    # 检查虚拟环境
    venv_ok = check_venv()
    
    # 显示启动说明
    show_startup_commands()
    
    # 总结
    print("\n📊 检查结果:")
    if files_ok and venv_ok:
        print("✅ 环境检查通过，可以启动API服务")
    else:
        print("❌ 环境检查失败，请检查缺失的文件或环境")
    
    print("\n💡 提示:")
    print("  1. 启动服务后，在浏览器中访问 http://localhost:8000/docs")
    print("  2. 使用Swagger UI可以直接测试所有API接口")
    print("  3. 按Ctrl+C停止服务")

if __name__ == "__main__":
    main()
