#!/usr/bin/env python3
"""
手动启动和测试脚本
"""
import subprocess
import sys
import time
import os
import requests

def run_command(cmd, background=False):
    """运行命令"""
    try:
        if background:
            return subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        else:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            return result
    except Exception as e:
        print(f"命令执行错误: {e}")
        return None

def check_backend():
    """检查后端服务"""
    try:
        response = requests.get("http://localhost:8000/api/v1/", timeout=3)
        if response.status_code == 200:
            print("✅ 后端服务正在运行")
            return True
    except:
        pass
    print("❌ 后端服务未运行")
    return False

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    # 切换到后端目录
    backend_dir = "/Users/<USER>/Desktop/code/xinfang/api-server"
    os.chdir(backend_dir)
    
    # 启动服务
    cmd = "source venv/bin/activate && python main.py"
    process = run_command(cmd, background=True)
    
    if process:
        print("⏳ 等待服务启动...")
        time.sleep(8)
        
        if check_backend():
            print("✅ 后端服务启动成功")
            return process
        else:
            print("❌ 后端服务启动失败")
            process.terminate()
            return None
    return None

def run_frontend_tests():
    """运行前端API测试"""
    print("🧪 运行前端API测试...")
    
    # 切换到项目根目录
    os.chdir("/Users/<USER>/Desktop/code/xinfang")
    
    # 运行测试脚本
    cmd = "python3 complete_frontend_api_test.py"
    result = run_command(cmd)
    
    if result and result.returncode == 0:
        print("✅ 前端API测试完成")
        print(result.stdout)
    else:
        print("❌ 前端API测试失败")
        if result:
            print(f"错误: {result.stderr}")

def main():
    """主函数"""
    print("🏛️ 中国信访系统 - 完整API测试启动器")
    print("=" * 50)
    
    backend_process = None
    
    try:
        # 1. 检查后端服务
        if not check_backend():
            # 2. 启动后端服务
            backend_process = start_backend()
            if not backend_process:
                print("❌ 无法启动后端服务，退出测试")
                return
        
        # 3. 运行前端测试
        run_frontend_tests()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断测试")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    finally:
        # 4. 清理资源
        if backend_process:
            print("🛑 停止后端服务...")
            backend_process.terminate()
            time.sleep(2)
        
        print("🎉 测试完成")

if __name__ == "__main__":
    main()
