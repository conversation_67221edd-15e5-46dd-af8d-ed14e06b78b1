#!/bin/bash

echo "🚀 中国信访系统 - 启动后端服务并运行完整API测试"
echo "============================================"

# 切换到后端目录
cd /Users/<USER>/Desktop/code/xinfang/api-server

# 激活虚拟环境
source venv/bin/activate

# 后台启动API服务
echo "📡 启动后端服务..."
python main.py &
BACKEND_PID=$!

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务是否启动成功
if curl -s http://localhost:8000/api/v1/ > /dev/null; then
    echo "✅ 后端服务启动成功"
    
    # 切换到项目根目录运行测试
    cd /Users/<USER>/Desktop/code/xinfang
    echo "🧪 运行前端API完整测试..."
    python3 complete_frontend_api_test.py
    
    echo "✅ 测试完成"
else
    echo "❌ 后端服务启动失败"
fi

# 停止后端服务
echo "🛑 停止后端服务..."
kill $BACKEND_PID

echo "🎉 所有任务完成"
