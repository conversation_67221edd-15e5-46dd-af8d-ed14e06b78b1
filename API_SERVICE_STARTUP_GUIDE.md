# 🚀 中国信访系统API服务启动指南

## 📋 快速启动步骤

### 方法1：使用Python启动脚本（推荐）
```bash
cd /Users/<USER>/Desktop/code/xinfang
python quick_start_api.py
```

### 方法2：手动启动
```bash
# 1. 进入API服务器目录
cd /Users/<USER>/Desktop/code/xinfang/api-server

# 2. 激活虚拟环境
source venv/bin/activate

# 3. 安装依赖（如果需要）
pip install -r requirements.txt

# 4. 启动服务
python main.py
```

### 方法3：使用bash脚本
```bash
cd /Users/<USER>/Desktop/code/xinfang
./start_api_service.sh
```

## 📄 API文档访问地址

服务启动成功后，可以通过以下地址访问API文档：

### 🌟 主要文档页面
- **Swagger UI**: http://localhost:8000/docs
  - 交互式API文档，可以直接测试API
  - 包含所有端点的详细说明和参数
  - 支持在线调试和测试

- **ReDoc**: http://localhost:8000/redoc
  - 美观的API文档展示
  - 更适合阅读和查看API规范

- **OpenAPI JSON**: http://localhost:8000/openapi.json
  - API规范的JSON格式
  - 可用于生成客户端代码

### 🔗 服务地址
- **主页**: http://localhost:8000
- **API基础URL**: http://localhost:8000/api/v1

## 🎯 主要API端点

### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/test-token` - 测试Token有效性

### 来访接待
- `GET /api/v1/visiting` - 获取来访列表
- `POST /api/v1/visiting` - 创建来访记录
- `GET /api/v1/visiting/{id}` - 获取来访详情
- `PUT /api/v1/visiting/{id}` - 更新来访记录
- `PUT /api/v1/visiting/{id}/status` - 更新来访状态
- `PUT /api/v1/visiting/{id}/assign` - 分配处理人

### 网上信访
- `GET /api/v1/online` - 获取网上信访列表
- `POST /api/v1/online` - 创建网上信访
- `GET /api/v1/online/{id}` - 获取网上信访详情
- `PUT /api/v1/online/{id}` - 更新网上信访
- `PUT /api/v1/online/{id}/status` - 更新状态
- `PUT /api/v1/online/{id}/assign` - 分配处理人

### 信件处理
- `GET /api/v1/letter/letters` - 获取信件列表
- `POST /api/v1/letter/letters` - 创建信件记录
- `GET /api/v1/letter/letters/{id}` - 获取信件详情
- `PUT /api/v1/letter/letters/{id}` - 更新信件
- `PUT /api/v1/letter/letters/{id}/status` - 更新状态
- `PUT /api/v1/letter/letters/{id}/assign` - 分配处理人

## 🔧 测试API

### 1. 使用Swagger UI测试
1. 访问 http://localhost:8000/docs
2. 点击任意API端点
3. 点击"Try it out"按钮
4. 填写参数
5. 点击"Execute"执行

### 2. 使用现有测试脚本
```bash
cd /Users/<USER>/Desktop/code/xinfang
python complete_frontend_api_test.py
```

### 3. 手动curl测试
```bash
# 登录获取token
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=test&password=test123"

# 获取来访列表
curl -X GET "http://localhost:8000/api/v1/visiting" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🐛 故障排除

### 端口被占用
如果8000端口被占用，修改main.py中的端口：
```python
uvicorn.run("main:app", host="0.0.0.0", port=8001, reload=True)
```

### 依赖问题
重新安装依赖：
```bash
cd /Users/<USER>/Desktop/code/xinfang/api-server
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### 数据库问题
重新创建数据库：
```bash
cd /Users/<USER>/Desktop/code/xinfang/api-server
python setup_db.py
```

## 📊 服务状态检查

服务启动后，终端会显示类似信息：
```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [12345] using StatReload
INFO:     Started server process [12346]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
```

看到这些信息表示服务启动成功！

## 🎉 API功能特性

✅ **完整的CRUD操作**
✅ **JWT认证和授权**
✅ **自动API文档生成**
✅ **跨域支持(CORS)**
✅ **数据验证和序列化**
✅ **错误处理和状态码**
✅ **SQLAlchemy ORM支持**
✅ **异步请求处理**

---
**提示**: 首次启动可能需要几秒钟初始化数据库，请耐心等待。
