#!/bin/bash
# 中国信访系统后端API服务启动脚本

echo "🚀 正在启动中国信访系统后端API服务..."
echo "=================================="

# 切换到API服务器目录
cd /Users/<USER>/Desktop/code/xinfang/api-server

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，正在创建..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "📦 检查并安装依赖..."
pip install -r requirements.txt

# 启动服务
echo "🌟 启动FastAPI服务..."
echo "=================================="
echo "📄 API文档地址:"
echo "   - Swagger UI: http://localhost:8000/docs"
echo "   - ReDoc: http://localhost:8000/redoc"
echo "   - OpenAPI JSON: http://localhost:8000/openapi.json"
echo "🔗 API基础URL: http://localhost:8000/api/v1"
echo "=================================="

# 运行服务
python main.py
