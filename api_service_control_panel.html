<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国信访系统API服务控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 30px;
            border-radius: 10px;
            border-left: 5px solid #3498db;
            background: #f8f9fa;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .status-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            border: 2px solid #e9ecef;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-checking { background: #ffc107; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            margin: 10px 10px 10px 0;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            color: white;
        }
        
        .btn-primary { background: #007bff; }
        .btn-primary:hover { background: #0056b3; }
        
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        
        .btn-info { background: #17a2b8; }
        .btn-info:hover { background: #138496; }
        
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        
        .command-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 14px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .api-list {
            list-style: none;
            padding: 0;
        }
        
        .api-item {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .api-method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .method-get { background: #28a745; color: white; }
        .method-post { background: #007bff; color: white; }
        .method-put { background: #ffc107; color: #212529; }
        .method-delete { background: #dc3545; color: white; }
        
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ 中国信访系统API服务控制台</h1>
            <p>后端API服务启动和管理中心</p>
        </div>
        
        <div class="content">
            <!-- 服务状态检查 -->
            <div class="section">
                <h2>🔍 服务状态检查</h2>
                <div class="status-card">
                    <div id="serviceStatus">
                        <span class="status-indicator status-checking"></span>
                        <span>正在检查服务状态...</span>
                    </div>
                    <div id="serviceDetails" style="margin-top: 15px; display: none;">
                        <p><strong>服务地址:</strong> <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></p>
                        <p><strong>API基础URL:</strong> <a href="http://localhost:8000/api/v1" target="_blank">http://localhost:8000/api/v1</a></p>
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="checkServiceStatus()">🔄 重新检查状态</button>
                <button class="btn btn-info" onclick="openAPIRoot()">🌐 打开服务主页</button>
            </div>
            
            <!-- API文档访问 -->
            <div class="section">
                <h2>📚 API文档访问</h2>
                <div class="grid">
                    <div class="card">
                        <h3>📖 Swagger UI</h3>
                        <p>交互式API文档，可以直接测试所有API接口</p>
                        <a href="http://localhost:8000/docs" target="_blank" class="btn btn-success">📖 打开 Swagger UI</a>
                    </div>
                    
                    <div class="card">
                        <h3>📄 ReDoc</h3>
                        <p>美观的API文档展示，更适合阅读和查看API规范</p>
                        <a href="http://localhost:8000/redoc" target="_blank" class="btn btn-info">📄 打开 ReDoc</a>
                    </div>
                    
                    <div class="card">
                        <h3>🔧 OpenAPI JSON</h3>
                        <p>API规范的JSON格式，可用于生成客户端代码</p>
                        <a href="http://localhost:8000/openapi.json" target="_blank" class="btn btn-warning">🔧 查看 OpenAPI</a>
                    </div>
                </div>
            </div>
            
            <!-- 启动服务 -->
            <div class="section">
                <h2>🚀 启动API服务</h2>
                <p>如果服务未运行，请使用以下方法启动：</p>
                
                <h3>方法1: 使用终端命令</h3>
                <div class="command-box">
cd /Users/<USER>/Desktop/code/xinfang/api-server
source venv/bin/activate
python main.py
                </div>
                
                <h3>方法2: 使用启动脚本</h3>
                <div class="command-box">
cd /Users/<USER>/Desktop/code/xinfang
python3 direct_start_api.py
                </div>
                
                <button class="btn btn-primary" onclick="copyCommand('cd /Users/<USER>/Desktop/code/xinfang/api-server && source venv/bin/activate && python main.py')">📋 复制启动命令</button>
            </div>
            
            <!-- API端点列表 -->
            <div class="section">
                <h2>🎯 主要API端点</h2>
                <ul class="api-list">
                    <li class="api-item">
                        <span class="api-method method-post">POST</span>
                        <strong>/api/v1/auth/login</strong> - 用户登录认证
                    </li>
                    <li class="api-item">
                        <span class="api-method method-get">GET</span>
                        <strong>/api/v1/auth/test-token</strong> - 测试Token有效性
                    </li>
                    <li class="api-item">
                        <span class="api-method method-get">GET</span>
                        <strong>/api/v1/visiting</strong> - 获取来访接待列表
                    </li>
                    <li class="api-item">
                        <span class="api-method method-post">POST</span>
                        <strong>/api/v1/visiting</strong> - 创建来访记录
                    </li>
                    <li class="api-item">
                        <span class="api-method method-get">GET</span>
                        <strong>/api/v1/online</strong> - 获取网上信访列表
                    </li>
                    <li class="api-item">
                        <span class="api-method method-post">POST</span>
                        <strong>/api/v1/online</strong> - 创建网上信访
                    </li>
                    <li class="api-item">
                        <span class="api-method method-get">GET</span>
                        <strong>/api/v1/letter/letters</strong> - 获取信件处理列表
                    </li>
                    <li class="api-item">
                        <span class="api-method method-post">POST</span>
                        <strong>/api/v1/letter/letters</strong> - 创建信件记录
                    </li>
                </ul>
                
                <button class="btn btn-success" onclick="testAPIs()">🧪 测试主要API</button>
                <button class="btn btn-info" onclick="runFullAPITest()">🔬 运行完整API测试</button>
            </div>
            
            <!-- 测试结果 -->
            <div class="section">
                <h2>📊 测试结果</h2>
                <div id="testResults" class="result-box" style="display: none;">
                    测试结果将在这里显示...
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 检查服务状态
        async function checkServiceStatus() {
            const statusElement = document.getElementById('serviceStatus');
            const detailsElement = document.getElementById('serviceDetails');
            
            statusElement.innerHTML = '<span class="status-indicator status-checking"></span><span>正在检查服务状态...</span>';
            detailsElement.style.display = 'none';
            
            try {
                const response = await fetch('http://localhost:8000', { 
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    statusElement.innerHTML = '<span class="status-indicator status-online"></span><span class="success">✅ API服务正在运行</span>';
                    detailsElement.style.display = 'block';
                } else {
                    throw new Error('服务响应异常');
                }
            } catch (error) {
                statusElement.innerHTML = '<span class="status-indicator status-offline"></span><span class="error">❌ API服务未运行或无法连接</span>';
                detailsElement.style.display = 'none';
            }
        }
        
        // 打开API根页面
        function openAPIRoot() {
            window.open('http://localhost:8000', '_blank');
        }
        
        // 复制命令到剪贴板
        function copyCommand(command) {
            navigator.clipboard.writeText(command).then(() => {
                alert('启动命令已复制到剪贴板！\n请在终端中粘贴并执行。');
            }).catch(() => {
                alert('复制失败，请手动复制命令：\n' + command);
            });
        }
        
        // 测试主要API
        async function testAPIs() {
            const resultsElement = document.getElementById('testResults');
            resultsElement.style.display = 'block';
            resultsElement.innerHTML = '🧪 正在测试API端点...\n';
            
            const endpoints = [
                { method: 'GET', url: 'http://localhost:8000', name: '根页面' },
                { method: 'GET', url: 'http://localhost:8000/api/v1/visiting', name: '来访接待列表' },
                { method: 'GET', url: 'http://localhost:8000/api/v1/online', name: '网上信访列表' },
                { method: 'GET', url: 'http://localhost:8000/api/v1/letter/letters', name: '信件处理列表' }
            ];
            
            for (let endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url, { method: endpoint.method });
                    if (response.ok || response.status === 401) { // 401是正常的，表示需要认证
                        resultsElement.innerHTML += `✅ ${endpoint.name}: ${response.status} ${response.statusText}\n`;
                    } else {
                        resultsElement.innerHTML += `❌ ${endpoint.name}: ${response.status} ${response.statusText}\n`;
                    }
                } catch (error) {
                    resultsElement.innerHTML += `❌ ${endpoint.name}: 连接失败 - ${error.message}\n`;
                }
            }
            
            resultsElement.innerHTML += '\n📝 测试完成！如果看到401状态码，表示接口需要认证，这是正常的。';
        }
        
        // 运行完整API测试
        function runFullAPITest() {
            alert('完整API测试需要在终端中运行:\n\ncd /Users/<USER>/Desktop/code/xinfang\npython3 complete_frontend_api_test.py\n\n这将测试所有前端API接口功能。');
        }
        
        // 页面加载时自动检查服务状态
        window.addEventListener('load', checkServiceStatus);
        
        // 每30秒自动检查一次状态
        setInterval(checkServiceStatus, 30000);
    </script>
</body>
</html>
