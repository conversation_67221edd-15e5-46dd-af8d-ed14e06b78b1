
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>API测试报告</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    background-color: #f5f5f5;
                }
                .container {
                    width: 90%;
                    margin: 0 auto;
                    padding: 20px;
                }
                .header {
                    background-color: #4CAF50;
                    color: white;
                    padding: 20px;
                    border-radius: 5px 5px 0 0;
                }
                .summary {
                    background-color: white;
                    padding: 20px;
                    margin-bottom: 20px;
                    border-radius: 0 0 5px 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .summary-table {
                    width: 100%;
                    border-collapse: collapse;
                }
                .summary-table th, .summary-table td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }
                .summary-table th {
                    background-color: #f2f2f2;
                }
                .results {
                    background-color: white;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .test-result {
                    margin-bottom: 10px;
                    padding: 10px;
                    border-radius: 5px;
                }
                .pass {
                    background-color: #dff0d8;
                    border: 1px solid #d6e9c6;
                }
                .fail {
                    background-color: #f2dede;
                    border: 1px solid #ebccd1;
                }
                .error {
                    background-color: #fcf8e3;
                    border: 1px solid #faebcc;
                }
                .test-details {
                    margin-top: 10px;
                    padding: 10px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                    white-space: pre-wrap;
                    overflow-x: auto;
                }
                .progress-bar {
                    height: 20px;
                    background-color: #f2f2f2;
                    border-radius: 5px;
                    margin-bottom: 10px;
                }
                .progress {
                    height: 100%;
                    background-color: #4CAF50;
                    border-radius: 5px;
                    width: 80.0%;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>公安信访智能化系统 API 测试报告</h1>
                    <p>测试时间: 2025-05-23 10:47:39</p>
                </div>
                
                <div class="summary">
                    <h2>测试摘要</h2>
                    <div class="progress-bar">
                        <div class="progress"></div>
                    </div>
                    <p>通过率: 80.00% (16/20)</p>
                    
                    <table class="summary-table">
                        <tr>
                            <th>测试总数</th>
                            <th>通过</th>
                            <th>失败</th>
                            <th>错误</th>
                        </tr>
                        <tr>
                            <td>20</td>
                            <td>16</td>
                            <td>4</td>
                            <td>0</td>
                        </tr>
                    </table>
                </div>
                
                <div class="results">
                    <h2>测试结果</h2>
        
            <div class="test-result fail">
                <h3>test_create_letter_petition (tests.test_api.LetterAPITestCase) - FAIL</h3>
            
                <div class="test-details">
                    Traceback (most recent call last):
  File &quot;/Users/<USER>/Desktop/code/xinfang/api-server/tests/test_api.py&quot;, line 535, in test_create_letter_petition
    self.assertEqual(response.status_code, 200, f&quot;创建信件信访失败: {response.text}&quot;)
AssertionError: 400 != 200 : 创建信件信访失败: {&quot;detail&quot;:&quot;信访类型必须为 &#x27;LETTER&#x27;&quot;}

                </div>
                </div>
            <div class="test-result fail">
                <h3>test_get_letter_petition_detail (tests.test_api.LetterAPITestCase) - FAIL</h3>
            
                <div class="test-details">
                    Traceback (most recent call last):
  File &quot;/Users/<USER>/Desktop/code/xinfang/api-server/tests/test_api.py&quot;, line 579, in test_get_letter_petition_detail
    created_id = self.test_create_letter_petition()
  File &quot;/Users/<USER>/Desktop/code/xinfang/api-server/tests/test_api.py&quot;, line 535, in test_create_letter_petition
    self.assertEqual(response.status_code, 200, f&quot;创建信件信访失败: {response.text}&quot;)
AssertionError: 400 != 200 : 创建信件信访失败: {&quot;detail&quot;:&quot;信访类型必须为 &#x27;LETTER&#x27;&quot;}

                </div>
                </div>
            <div class="test-result fail">
                <h3>test_get_letter_petitions (tests.test_api.LetterAPITestCase) - FAIL</h3>
            
                <div class="test-details">
                    Traceback (most recent call last):
  File &quot;/Users/<USER>/Desktop/code/xinfang/api-server/tests/test_api.py&quot;, line 552, in test_get_letter_petitions
    self.test_create_letter_petition()
  File &quot;/Users/<USER>/Desktop/code/xinfang/api-server/tests/test_api.py&quot;, line 535, in test_create_letter_petition
    self.assertEqual(response.status_code, 200, f&quot;创建信件信访失败: {response.text}&quot;)
AssertionError: 400 != 200 : 创建信件信访失败: {&quot;detail&quot;:&quot;信访类型必须为 &#x27;LETTER&#x27;&quot;}

                </div>
                </div>
            <div class="test-result fail">
                <h3>test_update_letter_petition_status (tests.test_api.LetterAPITestCase) - FAIL</h3>
            
                <div class="test-details">
                    Traceback (most recent call last):
  File &quot;/Users/<USER>/Desktop/code/xinfang/api-server/tests/test_api.py&quot;, line 599, in test_update_letter_petition_status
    created_id = self.test_create_letter_petition()
  File &quot;/Users/<USER>/Desktop/code/xinfang/api-server/tests/test_api.py&quot;, line 535, in test_create_letter_petition
    self.assertEqual(response.status_code, 200, f&quot;创建信件信访失败: {response.text}&quot;)
AssertionError: 400 != 200 : 创建信件信访失败: {&quot;detail&quot;:&quot;信访类型必须为 &#x27;LETTER&#x27;&quot;}

                </div>
                </div>
            <div class="test-result pass">
                <h3>成功通过的测试: 16个 - PASS</h3>
            </div>
                </div>
            </div>
        </body>
        </html>
        