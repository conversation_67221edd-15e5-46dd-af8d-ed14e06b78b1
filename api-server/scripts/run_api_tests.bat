@echo off
REM API测试启动脚本

echo ======== 公安信访智能化系统API测试 ========

REM 获取当前目录
set SCRIPT_DIR=%~dp0
set API_DIR=%SCRIPT_DIR%api-server

REM 检查是否有虚拟环境
if not exist "%API_DIR%\venv" (
    echo 创建Python虚拟环境...
    python -m venv "%API_DIR%\venv"
)

REM 激活虚拟环境
echo 激活虚拟环境...
call "%API_DIR%\venv\Scripts\activate.bat"

REM 安装依赖
echo 安装依赖包...
pip install -r "%API_DIR%\requirements.txt"

REM 启动API服务器
echo 启动API服务器...
cd "%API_DIR%"
python -m app.db.init_db
start /B python -m uvicorn main:app --host 0.0.0.0 --port 8000 > "%TEMP%\api_server.log" 2>&1
set API_PID=!ERRORLEVEL!

echo API服务器已启动

REM 等待API服务器启动
echo 等待API服务器启动 (5秒)...
timeout /t 5 /nobreak > nul

REM 运行测试
echo 开始运行API测试...
python "%API_DIR%\tests\test_api.py"
set TEST_RESULT=%ERRORLEVEL%

REM 结束API服务器进程 (在Windows中比较复杂，这里简化处理)
echo 停止API服务器...
taskkill /F /FI "WINDOWTITLE eq uvicorn*" /T > nul 2>&1

REM 输出测试结果
if %TEST_RESULT% EQU 0 (
    echo API测试全部通过!
) else (
    echo API测试未全部通过，请检查详细输出。
)

exit /B %TEST_RESULT%
