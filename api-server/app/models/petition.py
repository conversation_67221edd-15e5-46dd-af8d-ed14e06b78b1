from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Enum, ARRAY
from sqlalchemy.orm import relationship

from app.db.database import Base


# 信访类型枚举：来访登记、在线信访、信件处理
class PetitionType:
    VISITING = "VISITING"  # 来访登记
    ONLINE = "ONLINE"      # 在线信访
    LETTER = "LETTER"      # 信件处理


# 信访状态枚举：待处理、处理中、已处理、已归档
class PetitionStatus:
    PENDING = "PENDING"       # 待处理
    PROCESSING = "PROCESSING" # 处理中
    COMPLETED = "COMPLETED"   # 已处理
    CLOSED = "CLOSED"         # 已关闭


# 信访记录表 - 基础表
class Petition(Base):
    """信访记录表 - 基础信息"""
    __tablename__ = "petitions"

    id = Column(Integer, primary_key=True, index=True)
    petition_no = Column(String(50), unique=True, index=True, nullable=False, comment="信访编号")
    type = Column(String(20), nullable=False, comment="信访类型")
    title = Column(String(200), nullable=False, comment="信访标题")
    content = Column(Text, nullable=True, comment="信访内容")

    petitioner_name = Column(String(50), nullable=False, comment="信访人姓名")
    petitioner_phone = Column(String(20), nullable=True, comment="信访人电话")
    petitioner_id_card = Column(String(20), nullable=True, comment="信访人身份证")
    petitioner_address = Column(String(200), nullable=True, comment="信访人地址")

    status = Column(String(20), default=PetitionStatus.PENDING, nullable=False, comment="处理状态")
    priority = Column(Integer, default=0, comment="优先级")
    is_sensitive = Column(Boolean, default=False, comment="是否敏感")

    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 处理人ID
    handler_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="处理人ID")
    handler = relationship("User", backref="handled_petitions")

    # 类型特定信息
    visiting_info = relationship("VisitingPetition", uselist=False, back_populates="petition")
    online_info = relationship("OnlinePetition", uselist=False, back_populates="petition")
    letter_info = relationship("LetterPetition", uselist=False, back_populates="petition")

    # 处理记录
    processing_records = relationship("ProcessingRecord", back_populates="petition")


# 来访信访表 - 扩展信息
class VisitingPetition(Base):
    """来访信访表 - 扩展信息"""
    __tablename__ = "visiting_petitions"

    id = Column(Integer, primary_key=True, index=True)
    petition_id = Column(Integer, ForeignKey("petitions.id"), nullable=False, unique=True)

    reception_time = Column(DateTime, nullable=False, comment="接待时间")
    reception_location = Column(String(100), nullable=True, comment="接待地点")
    visitor_count = Column(Integer, default=1, comment="来访人数")
    is_group_visit = Column(Boolean, default=False, comment="是否集体访")
    has_id_verified = Column(Boolean, default=False, comment="是否已身份核验")

    petition = relationship("Petition", back_populates="visiting_info")


# 在线信访表 - 扩展信息
class OnlinePetition(Base):
    """在线信访表 - 扩展信息"""
    __tablename__ = "online_petitions"

    id = Column(Integer, primary_key=True, index=True)
    petition_id = Column(Integer, ForeignKey("petitions.id"), nullable=False, unique=True)

    ip_address = Column(String(50), nullable=True, comment="IP地址")
    platform = Column(String(50), nullable=True, comment="平台信息")
    attachment_urls = Column(Text, nullable=True, comment="附件URL列表，以逗号分隔")

    petition = relationship("Petition", back_populates="online_info")


# 信件信访表 - 扩展信息
class LetterPetition(Base):
    """信件信访表 - 扩展信息"""
    __tablename__ = "letter_petitions"

    id = Column(Integer, primary_key=True, index=True)
    petition_id = Column(Integer, ForeignKey("petitions.id"), nullable=False, unique=True)

    received_date = Column(DateTime, nullable=False, comment="收信日期")
    letter_source = Column(String(100), nullable=True, comment="信件来源")
    has_attachments = Column(Boolean, default=False, comment="是否有附件")
    attachment_description = Column(Text, nullable=True, comment="附件描述")

    petition = relationship("Petition", back_populates="letter_info")


# 处理记录表
class ProcessingRecord(Base):
    """处理记录表"""
    __tablename__ = "processing_records"

    id = Column(Integer, primary_key=True, index=True)
    petition_id = Column(Integer, ForeignKey("petitions.id"), nullable=False)

    handler_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="处理人ID")
    action = Column(String(50), nullable=False, comment="处理行为")
    note = Column(Text, nullable=True, comment="处理备注")
    created_at = Column(DateTime, default=datetime.now, comment="处理时间")

    petition = relationship("Petition", back_populates="processing_records")
    handler = relationship("User", backref="processing_records")
