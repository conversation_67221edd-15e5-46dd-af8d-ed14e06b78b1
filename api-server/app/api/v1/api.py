"""
API路由入口，包含所有API的集合
"""
from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, petitions, visiting, online, letter, statistics, search, menu, upload

# API路由
api_router = APIRouter()

# 认证路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])

# 用户路由
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])

# 信访路由
api_router.include_router(petitions.router, prefix="/petitions", tags=["信访管理"])

# 来访接待路由
api_router.include_router(visiting.router, prefix="/visiting", tags=["来访接待"])

# 在线信访路由
api_router.include_router(online.router, prefix="/online", tags=["在线信访"])

# 信件处理路由
api_router.include_router(letter.router, prefix="/letter", tags=["信件处理"])

# 统计分析路由
api_router.include_router(statistics.router, prefix="/statistics", tags=["统计分析"])

# 搜索路由
api_router.include_router(search.router, prefix="/search", tags=["智能搜索"])

# 菜单路由
api_router.include_router(menu.router, prefix="/menu", tags=["菜单管理"])
api_router.include_router(upload.router, prefix="/upload", tags=["文件上传"])
