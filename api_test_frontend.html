<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信访系统 API 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 20px;
        }
        .test-section h2 {
            color: #262626;
            margin-top: 0;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #52c41a;
            background: #f6ffed;
        }
        .error {
            border-left: 4px solid #ff4d4f;
            background: #fff2f0;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success { background: #52c41a; color: white; }
        .status.error { background: #ff4d4f; color: white; }
        .status.pending { background: #faad14; color: white; }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            margin: 5px 0;
            box-sizing: border-box;
        }
        .form-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .form-row > div {
            flex: 1;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #262626;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏛️ 中国信访智能化系统 API 测试平台</h1>
        
        <!-- 认证部分 -->
        <div class="test-section">
            <h2>🔐 用户认证</h2>
            <div class="form-row">
                <div>
                    <label>用户名</label>
                    <input type="text" id="username" value="test" placeholder="输入用户名">
                </div>
                <div>
                    <label>密码</label>
                    <input type="password" id="password" value="test123" placeholder="输入密码">
                </div>
            </div>
            <button onclick="login()">登录获取Token</button>
            <button onclick="testToken()">测试Token</button>
            <div id="authResult" class="result"></div>
        </div>

        <!-- 来访信访测试 -->
        <div class="test-section">
            <h2>👥 来访信访管理</h2>
            <div class="form-row">
                <div>
                    <label>信访ID</label>
                    <input type="number" id="visitingId" value="30" placeholder="来访信访ID">
                </div>
                <div>
                    <label>新状态</label>
                    <select id="visitingStatus">
                        <option value="待处理">待处理</option>
                        <option value="处理中">处理中</option>
                        <option value="已解决">已解决</option>
                        <option value="已关闭">已关闭</option>
                    </select>
                </div>
            </div>
            <div>
                <label>处理意见</label>
                <textarea id="visitingNote" rows="3" placeholder="输入处理意见或备注"></textarea>
            </div>
            <button onclick="getVisitingList()">获取来访列表</button>
            <button onclick="getVisitingDetail()">获取来访详情</button>
            <button onclick="updateVisitingStatus()">更新状态</button>
            <button onclick="createVisitingPetition()">创建来访记录</button>
            <div id="visitingResult" class="result"></div>
        </div>

        <!-- 在线信访测试 -->
        <div class="test-section">
            <h2>💻 在线信访管理</h2>
            <div class="form-row">
                <div>
                    <label>信访ID</label>
                    <input type="number" id="onlineId" value="31" placeholder="在线信访ID">
                </div>
                <div>
                    <label>新状态</label>
                    <select id="onlineStatus">
                        <option value="待处理">待处理</option>
                        <option value="处理中">处理中</option>
                        <option value="已解决">已解决</option>
                        <option value="已关闭">已关闭</option>
                    </select>
                </div>
            </div>
            <div>
                <label>处理意见</label>
                <textarea id="onlineNote" rows="3" placeholder="输入处理意见或备注"></textarea>
            </div>
            <button onclick="getOnlineList()">获取在线列表</button>
            <button onclick="getOnlineDetail()">获取在线详情</button>
            <button onclick="updateOnlineStatus()">更新状态</button>
            <button onclick="createOnlinePetition()">创建在线记录</button>
            <div id="onlineResult" class="result"></div>
        </div>

        <!-- 信件信访测试 -->
        <div class="test-section">
            <h2>📮 信件信访管理</h2>
            <div class="form-row">
                <div>
                    <label>信访ID</label>
                    <input type="number" id="letterId" value="1" placeholder="信件信访ID">
                </div>
                <div>
                    <label>新状态</label>
                    <select id="letterStatus">
                        <option value="待处理">待处理</option>
                        <option value="处理中">处理中</option>
                        <option value="已解决">已解决</option>
                        <option value="已关闭">已关闭</option>
                    </select>
                </div>
            </div>
            <div>
                <label>处理意见</label>
                <textarea id="letterNote" rows="3" placeholder="输入处理意见或备注"></textarea>
            </div>
            <button onclick="getLetterList()">获取信件列表</button>
            <button onclick="getLetterDetail()">获取信件详情</button>
            <button onclick="updateLetterStatus()">更新状态</button>
            <div id="letterResult" class="result"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h2>🧪 综合API测试</h2>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="clearAllResults()">清空结果</button>
            <div id="allTestsResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let authToken = null;

        // 通用请求函数
        async function apiRequest(url, options = {}) {
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken ? { 'Authorization': `Bearer ${authToken}` } : {}),
                    ...options.headers
                },
                ...options
            };

            try {
                const response = await fetch(`${API_BASE}${url}`, config);
                const data = await response.json();
                
                return {
                    status: response.status,
                    ok: response.ok,
                    data: data
                };
            } catch (error) {
                return {
                    status: 0,
                    ok: false,
                    error: error.message
                };
            }
        }

        // 显示结果
        function showResult(elementId, result, operation) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            
            let resultClass = result.ok ? 'success' : 'error';
            let statusBadge = `<span class="status ${resultClass}">${result.status}</span>`;
            
            const output = `[${timestamp}] ${operation}\n${statusBadge}\n${JSON.stringify(result, null, 2)}`;
            
            element.innerHTML = output;
            element.className = `result ${resultClass}`;
        }

        // 认证相关
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            const formData = new URLSearchParams();
            formData.append('username', username);
            formData.append('password', password);

            const result = await apiRequest('/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: formData
            });

            if (result.ok) {
                authToken = result.data.access_token;
                showResult('authResult', result, '登录成功');
            } else {
                showResult('authResult', result, '登录失败');
            }
        }

        async function testToken() {
            if (!authToken) {
                showResult('authResult', {ok: false, error: '请先登录获取Token'}, 'Token测试');
                return;
            }

            const result = await apiRequest('/auth/test-token', {
                method: 'POST'
            });
            
            showResult('authResult', result, 'Token测试');
        }

        // 来访信访相关
        async function getVisitingList() {
            const result = await apiRequest('/visiting');
            showResult('visitingResult', result, '获取来访列表');
        }

        async function getVisitingDetail() {
            const id = document.getElementById('visitingId').value;
            const result = await apiRequest(`/visiting/${id}`);
            showResult('visitingResult', result, `获取来访详情 (ID: ${id})`);
        }

        async function updateVisitingStatus() {
            const id = document.getElementById('visitingId').value;
            const status = document.getElementById('visitingStatus').value;
            const note = document.getElementById('visitingNote').value;

            const result = await apiRequest(`/visiting/${id}/status`, {
                method: 'PUT',
                body: JSON.stringify({ status, note })
            });
            
            showResult('visitingResult', result, `更新来访状态 (ID: ${id})`);
        }

        async function createVisitingPetition() {
            const data = {
                title: "前端测试来访信访",
                content: "这是通过前端页面创建的测试来访信访记录",
                type: "visiting",
                petitioner_name: "前端测试用户",
                petitioner_phone: "13800138888",
                petitioner_address: "前端测试地址",
                visiting_info: {
                    reception_time: new Date().toISOString(),
                    reception_location: "信访接待室",
                    visitor_count: 1,
                    is_group_visit: false,
                    has_id_verified: true
                }
            };

            const result = await apiRequest('/visiting', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            showResult('visitingResult', result, '创建来访记录');
        }

        // 在线信访相关
        async function getOnlineList() {
            const result = await apiRequest('/online');
            showResult('onlineResult', result, '获取在线列表');
        }

        async function getOnlineDetail() {
            const id = document.getElementById('onlineId').value;
            const result = await apiRequest(`/online/${id}`);
            showResult('onlineResult', result, `获取在线详情 (ID: ${id})`);
        }

        async function updateOnlineStatus() {
            const id = document.getElementById('onlineId').value;
            const status = document.getElementById('onlineStatus').value;
            const note = document.getElementById('onlineNote').value;

            const result = await apiRequest(`/online/${id}/status`, {
                method: 'PUT',
                body: JSON.stringify({ status, note })
            });
            
            showResult('onlineResult', result, `更新在线状态 (ID: ${id})`);
        }

        async function createOnlinePetition() {
            const data = {
                title: "前端测试在线信访",
                content: "这是通过前端页面创建的测试在线信访记录",
                type: "online",
                petitioner_name: "在线测试用户",
                petitioner_phone: "13900139999",
                petitioner_address: "在线测试地址",
                online_info: {
                    ip_address: "*************",
                    platform: "前端测试平台",
                    attachment_urls: ""
                }
            };

            const result = await apiRequest('/online', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            showResult('onlineResult', result, '创建在线记录');
        }

        // 信件信访相关
        async function getLetterList() {
            const result = await apiRequest('/letter/letters');
            showResult('letterResult', result, '获取信件列表');
        }

        async function getLetterDetail() {
            const id = document.getElementById('letterId').value;
            const result = await apiRequest(`/letter/letters/${id}`);
            showResult('letterResult', result, `获取信件详情 (ID: ${id})`);
        }

        async function updateLetterStatus() {
            const id = document.getElementById('letterId').value;
            const status = document.getElementById('letterStatus').value;
            const note = document.getElementById('letterNote').value;

            const result = await apiRequest(`/letter/letters/${id}/status`, {
                method: 'PUT',
                body: JSON.stringify({ status, note })
            });
            
            showResult('letterResult', result, `更新信件状态 (ID: ${id})`);
        }

        // 综合测试
        async function runAllTests() {
            const results = [];
            const element = document.getElementById('allTestsResult');
            element.innerHTML = '正在运行综合测试...\n';
            
            // 1. 登录测试
            await login();
            
            // 2. 获取各类型列表
            results.push(await apiRequest('/visiting'));
            results.push(await apiRequest('/online'));
            results.push(await apiRequest('/letter/letters'));
            
            // 3. 状态更新测试
            results.push(await apiRequest('/visiting/30/status', {
                method: 'PUT',
                body: JSON.stringify({ status: '测试状态', note: '综合测试备注' })
            }));
            
            results.push(await apiRequest('/online/31/status', {
                method: 'PUT',
                body: JSON.stringify({ status: '测试状态', note: '综合测试备注' })
            }));
            
            results.push(await apiRequest('/letter/letters/1/status', {
                method: 'PUT',
                body: JSON.stringify({ status: '测试状态', note: '综合测试备注' })
            }));
            
            const successCount = results.filter(r => r.ok).length;
            const totalCount = results.length;
            
            const summary = `
综合测试完成！
成功: ${successCount}/${totalCount}
时间: ${new Date().toLocaleString()}

详细结果:
${results.map((r, i) => `${i + 1}. ${r.ok ? '✅' : '❌'} 状态码: ${r.status}`).join('\n')}
`;
            
            showResult('allTestsResult', {
                ok: successCount === totalCount,
                summary: summary,
                results: results
            }, '综合测试');
        }

        function clearAllResults() {
            const resultElements = document.querySelectorAll('.result');
            resultElements.forEach(element => {
                element.innerHTML = '';
                element.className = 'result';
            });
        }

        // 页面加载完成后自动尝试登录
        window.onload = function() {
            console.log('🚀 信访系统 API 测试平台已加载');
            console.log('💡 提示: 可以直接点击按钮测试各个API功能');
        };
    </script>
</body>
</html>
