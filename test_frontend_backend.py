#!/usr/bin/env python3
"""
前端到后端API连接测试脚本
测试中国信访智能化系统的前端到后端完整功能
"""

import requests
import json
import sys
from datetime import datetime

# API配置
API_BASE_URL = "http://localhost:8000/api/v1"
TEST_USERNAME = "test"
TEST_PASSWORD = "test123"

class XinfangAPITester:
    def __init__(self):
        self.token = None
        self.session = requests.Session()
    
    def log(self, message, status="INFO"):
        timestamp = datetime.now().strftime("%H:%M:%S")
        status_colors = {
            "INFO": "\033[94m",    # 蓝色
            "SUCCESS": "\033[92m", # 绿色  
            "ERROR": "\033[91m",   # 红色
            "WARNING": "\033[93m"  # 黄色
        }
        reset_color = "\033[0m"
        print(f"{status_colors.get(status, '')}{timestamp} [{status}] {message}{reset_color}")
    
    def login(self):
        """登录获取访问令牌"""
        self.log("开始登录认证...")
        
        data = {
            'username': TEST_USERNAME,
            'password': TEST_PASSWORD
        }
        
        try:
            response = self.session.post(
                f"{API_BASE_URL}/auth/login",
                data=data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'}
            )
            
            if response.status_code == 200:
                result = response.json()
                self.token = result['access_token']
                self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                self.log(f"登录成功！Token: {self.token[:20]}...", "SUCCESS")
                return True
            else:
                self.log(f"登录失败: {response.status_code} - {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"登录异常: {str(e)}", "ERROR")
            return False
    
    def test_visiting_petitions(self):
        """测试来访信访功能"""
        self.log("=== 测试来访信访管理 ===")
        
        # 1. 获取来访列表
        try:
            response = self.session.get(f"{API_BASE_URL}/visiting")
            if response.status_code == 200:
                data = response.json()
                self.log(f"✅ 获取来访列表成功: 共{data['total']}条记录", "SUCCESS")
                
                # 如果有数据，测试状态更新
                if data['items']:
                    petition_id = data['items'][0]['id']
                    
                    # 2. 测试状态更新（包含note字段）
                    update_data = {
                        "status": "已解决",
                        "note": f"前端测试脚本更新 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 问题已彻底解决"
                    }
                    
                    response = self.session.put(
                        f"{API_BASE_URL}/visiting/{petition_id}/status",
                        json=update_data
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        self.log(f"✅ 来访信访状态更新成功 (ID: {petition_id})", "SUCCESS")
                        
                        # 验证处理记录中包含note
                        if result.get('processing_records'):
                            latest_record = result['processing_records'][-1]
                            if update_data['note'] in latest_record['note']:
                                self.log("✅ Note字段正确保存到处理记录中", "SUCCESS")
                            else:
                                self.log("❌ Note字段未正确保存", "ERROR")
                    else:
                        self.log(f"❌ 来访信访状态更新失败: {response.status_code}", "ERROR")
                        
            else:
                self.log(f"❌ 获取来访列表失败: {response.status_code}", "ERROR")
                
        except Exception as e:
            self.log(f"来访信访测试异常: {str(e)}", "ERROR")
    
    def test_online_petitions(self):
        """测试在线信访功能"""
        self.log("=== 测试在线信访管理 ===")
        
        try:
            # 1. 获取在线列表
            response = self.session.get(f"{API_BASE_URL}/online")
            if response.status_code == 200:
                data = response.json()
                self.log(f"✅ 获取在线列表成功: 共{data['total']}条记录", "SUCCESS")
                
                if data['items']:
                    petition_id = data['items'][0]['id']
                    
                    # 2. 测试状态更新
                    update_data = {
                        "status": "处理中",
                        "note": f"前端测试脚本 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 在线信访正在处理中"
                    }
                    
                    response = self.session.put(
                        f"{API_BASE_URL}/online/{petition_id}/status",
                        json=update_data
                    )
                    
                    if response.status_code == 200:
                        self.log(f"✅ 在线信访状态更新成功 (ID: {petition_id})", "SUCCESS")
                    else:
                        self.log(f"❌ 在线信访状态更新失败: {response.status_code}", "ERROR")
                        
            else:
                self.log(f"❌ 获取在线列表失败: {response.status_code}", "ERROR")
                
        except Exception as e:
            self.log(f"在线信访测试异常: {str(e)}", "ERROR")
    
    def test_letter_petitions(self):
        """测试信件信访功能"""
        self.log("=== 测试信件信访管理 ===")
        
        try:
            # 1. 获取信件列表
            response = self.session.get(f"{API_BASE_URL}/letter/letters")
            if response.status_code == 200:
                data = response.json()
                self.log(f"✅ 获取信件列表成功: 共{data['total']}条记录", "SUCCESS")
                
                if data['items']:
                    petition_id = data['items'][0]['id']
                    
                    # 2. 测试状态更新
                    update_data = {
                        "status": "已解决",
                        "note": f"前端测试脚本 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 信件信访处理完成"
                    }
                    
                    response = self.session.put(
                        f"{API_BASE_URL}/letter/letters/{petition_id}/status",
                        json=update_data
                    )
                    
                    if response.status_code == 200:
                        self.log(f"✅ 信件信访状态更新成功 (ID: {petition_id})", "SUCCESS")
                    else:
                        self.log(f"❌ 信件信访状态更新失败: {response.status_code}", "ERROR")
                        
            else:
                self.log(f"❌ 获取信件列表失败: {response.status_code}", "ERROR")
                
        except Exception as e:
            self.log(f"信件信访测试异常: {str(e)}", "ERROR")
    
    def test_create_petitions(self):
        """测试创建信访记录功能"""
        self.log("=== 测试创建信访记录 ===")
        
        # 1. 创建来访信访
        visiting_data = {
            "title": "前端API测试来访信访",
            "content": "这是通过前端API测试脚本创建的来访信访记录",
            "type": "visiting",
            "petitioner_name": "API测试用户",
            "petitioner_phone": "13812345678",
            "petitioner_address": "API测试地址",
            "visiting_info": {
                "reception_time": datetime.now().isoformat(),
                "reception_location": "API测试接待室",
                "visitor_count": 1,
                "is_group_visit": False,
                "has_id_verified": True
            }
        }
        
        try:
            response = self.session.post(f"{API_BASE_URL}/visiting", json=visiting_data)
            if response.status_code == 200:
                result = response.json()
                self.log(f"✅ 创建来访信访成功 (ID: {result['id']})", "SUCCESS")
            else:
                self.log(f"❌ 创建来访信访失败: {response.status_code}", "ERROR")
        except Exception as e:
            self.log(f"创建来访信访异常: {str(e)}", "ERROR")
        
        # 2. 创建在线信访
        online_data = {
            "title": "前端API测试在线信访",
            "content": "这是通过前端API测试脚本创建的在线信访记录",
            "type": "online",
            "petitioner_name": "在线API测试用户",
            "petitioner_phone": "13987654321",
            "petitioner_address": "在线API测试地址",
            "online_info": {
                "ip_address": "192.168.1.999",
                "platform": "API测试平台",
                "attachment_urls": ""
            }
        }
        
        try:
            response = self.session.post(f"{API_BASE_URL}/online", json=online_data)
            if response.status_code == 200:
                result = response.json()
                self.log(f"✅ 创建在线信访成功 (ID: {result['id']})", "SUCCESS")
            else:
                self.log(f"❌ 创建在线信访失败: {response.status_code}", "ERROR")
        except Exception as e:
            self.log(f"创建在线信访异常: {str(e)}", "ERROR")
    
    def run_all_tests(self):
        """运行所有测试"""
        self.log("🚀 开始运行中国信访智能化系统前端到后端API测试")
        self.log("=" * 60)
        
        # 1. 登录认证
        if not self.login():
            self.log("登录失败，无法继续测试", "ERROR")
            return False
        
        # 2. 测试各种信访类型
        self.test_visiting_petitions()
        self.test_online_petitions()
        self.test_letter_petitions()
        
        # 3. 测试创建功能
        self.test_create_petitions()
        
        self.log("=" * 60)
        self.log("🎉 API测试完成！", "SUCCESS")
        
        return True

if __name__ == "__main__":
    tester = XinfangAPITester()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)
