#!/usr/bin/env python3
"""
中国信访系统 - 综合API测试脚本
测试前端到后端的所有API连接和note字段功能
"""
import requests
import json
import sys
from datetime import datetime

API_BASE = "http://localhost:8000/api/v1"
TEST_USER = {"username": "test", "password": "test123"}

def test_auth():
    """测试认证功能"""
    print("🔐 测试用户认证...")
    try:
        response = requests.post(f"{API_BASE}/auth/login", data=TEST_USER)
        if response.status_code == 200:
            token = response.json()["access_token"]
            print(f"✅ 认证成功，获取到token: {token[:20]}...")
            return token
        else:
            print(f"❌ 认证失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 认证异常: {e}")
        return None

def test_visiting_apis(token):
    """测试来访信访API"""
    print("\n👥 测试来访信访API...")
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    try:
        # 获取列表
        response = requests.get(f"{API_BASE}/visiting", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取来访列表成功，共{data['total']}条记录")
            
            # 测试状态更新（包含note字段）
            if data['total'] > 0:
                petition_id = data['items'][0]['id']
                update_data = {
                    "status": "已解决",
                    "note": f"综合测试更新 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 问题已解决，测试note字段功能正常"
                }
                
                response = requests.put(f"{API_BASE}/visiting/{petition_id}/status", 
                                      headers=headers, json=update_data)
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 来访状态更新成功 (ID: {petition_id})")
                    print(f"   新状态: {result['status']}")
                    print(f"   处理记录数: {len(result['processing_records'])}")
                    if result['processing_records']:
                        latest = result['processing_records'][-1]
                        print(f"   最新记录note: {latest['note'][:50]}...")
                else:
                    print(f"❌ 来访状态更新失败: {response.status_code}")
        else:
            print(f"❌ 获取来访列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 来访API测试异常: {e}")

def test_online_apis(token):
    """测试在线信访API"""
    print("\n💻 测试在线信访API...")
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    try:
        # 获取列表
        response = requests.get(f"{API_BASE}/online", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取在线列表成功，共{data['total']}条记录")
            
            # 测试状态更新（包含note字段）
            if data['total'] > 0:
                petition_id = data['items'][0]['id']
                update_data = {
                    "status": "处理中",
                    "note": f"在线信访综合测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 已分配专人处理，note字段测试"
                }
                
                response = requests.put(f"{API_BASE}/online/{petition_id}/status", 
                                      headers=headers, json=update_data)
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 在线状态更新成功 (ID: {petition_id})")
                    print(f"   新状态: {result['status']}")
                    print(f"   处理记录数: {len(result['processing_records'])}")
                else:
                    print(f"❌ 在线状态更新失败: {response.status_code}")
        else:
            print(f"❌ 获取在线列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 在线API测试异常: {e}")

def test_letter_apis(token):
    """测试信件信访API"""
    print("\n📮 测试信件信访API...")
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    try:
        # 获取列表
        response = requests.get(f"{API_BASE}/letter/letters", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取信件列表成功，共{data['total']}条记录")
            
            # 测试状态更新（包含note字段）
            if data['total'] > 0:
                petition_id = data['items'][0]['id']
                update_data = {
                    "status": "已关闭",
                    "note": f"信件信访综合测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 处理完成并关闭，验证note字段功能"
                }
                
                response = requests.put(f"{API_BASE}/letter/letters/{petition_id}/status", 
                                      headers=headers, json=update_data)
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 信件状态更新成功 (ID: {petition_id})")
                    print(f"   新状态: {result['status']}")
                    print(f"   处理记录数: {len(result['processing_records'])}")
                else:
                    print(f"❌ 信件状态更新失败: {response.status_code}")
        else:
            print(f"❌ 获取信件列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 信件API测试异常: {e}")

def create_test_records(token):
    """创建测试记录"""
    print("\n📝 创建测试记录...")
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    # 创建来访记录
    visiting_data = {
        "title": "综合测试来访信访",
        "content": "这是综合测试脚本创建的来访信访记录，用于验证前后端连接",
        "type": "visiting",
        "petitioner_name": "测试用户",
        "petitioner_phone": "13800138000",
        "petitioner_address": "测试地址",
        "visiting_info": {
            "reception_time": datetime.now().isoformat(),
            "reception_location": "信访接待室",
            "visitor_count": 1,
            "is_group_visit": False,
            "has_id_verified": True
        }
    }
    
    try:
        response = requests.post(f"{API_BASE}/visiting", headers=headers, json=visiting_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 创建来访记录成功 (ID: {result['id']})")
        else:
            print(f"❌ 创建来访记录失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 创建来访记录异常: {e}")
    
    # 创建在线记录
    online_data = {
        "title": "综合测试在线信访",
        "content": "这是综合测试脚本创建的在线信访记录，用于验证前后端连接",
        "type": "online",
        "petitioner_name": "在线测试用户",
        "petitioner_phone": "13900139000",
        "petitioner_address": "在线测试地址",
        "online_info": {
            "ip_address": "*************",
            "platform": "综合测试平台",
            "attachment_urls": ""
        }
    }
    
    try:
        response = requests.post(f"{API_BASE}/online", headers=headers, json=online_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 创建在线记录成功 (ID: {result['id']})")
        else:
            print(f"❌ 创建在线记录失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 创建在线记录异常: {e}")

def main():
    print("🏛️ 中国信访智能化系统 - 综合API测试")
    print("=" * 60)
    
    # 1. 认证测试
    token = test_auth()
    if not token:
        print("❌ 认证失败，终止测试")
        sys.exit(1)
    
    # 2. 测试各类型API
    test_visiting_apis(token)
    test_online_apis(token)
    test_letter_apis(token)
    
    # 3. 创建测试记录
    create_test_records(token)
    
    print("\n" + "=" * 60)
    print("🎉 综合API测试完成！")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("✅ 所有API功能正常，前端到后端连接成功")
    print("✅ note字段功能在所有信访类型中都正常工作")

if __name__ == "__main__":
    main()
