#!/usr/bin/env python3
"""
Vue.js前端启动和连接测试脚本
"""
import subprocess
import time
import requests
import webbrowser
import os

def check_backend():
    """检查后端服务状态"""
    try:
        response = requests.get("http://localhost:8000/api/v1/", timeout=3)
        if response.status_code == 200:
            print("✅ 后端服务正在运行")
            return True
    except:
        pass
    print("❌ 后端服务未运行，请先启动后端服务")
    return False

def check_frontend():
    """检查前端服务状态"""
    try:
        response = requests.get("http://localhost:3000", timeout=3)
        if response.status_code == 200:
            print("✅ 前端服务正在运行")
            return True
    except:
        pass
    print("🔄 前端服务未运行")
    return False

def start_frontend():
    """启动Vue.js前端服务"""
    print("🚀 启动Vue.js前端开发服务器...")
    
    # 切换到前端目录
    frontend_dir = "/Users/<USER>/Desktop/code/xinfang/admin-web"
    os.chdir(frontend_dir)
    
    # 检查是否安装了依赖
    if not os.path.exists("node_modules"):
        print("📦 安装前端依赖...")
        install_result = subprocess.run("npm install", shell=True, capture_output=True, text=True)
        if install_result.returncode != 0:
            print("❌ 依赖安装失败")
            print(install_result.stderr)
            return None
    
    # 启动开发服务器
    print("🔄 启动开发服务器...")
    process = subprocess.Popen("npm run dev", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # 等待服务启动
    print("⏳ 等待前端服务启动...")
    for i in range(30):  # 等待最多30秒
        time.sleep(1)
        if check_frontend():
            print("✅ 前端服务启动成功")
            return process
        if i % 5 == 0:
            print(f"⏳ 等待中... ({i+1}/30秒)")
    
    print("❌ 前端服务启动超时")
    process.terminate()
    return None

def create_connection_test():
    """创建前端后端连接测试页面"""
    test_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国信访系统 - 前后端连接测试</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 15px; padding: 30px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; font-size: 2.5em; }
        .test-section { margin: 20px 0; padding: 20px; border: 2px solid #ecf0f1; border-radius: 10px; }
        .test-section h3 { color: #34495e; margin-top: 0; }
        button { background: #3498db; color: white; border: none; padding: 12px 25px; border-radius: 6px; cursor: pointer; margin: 5px; font-size: 16px; }
        button:hover { background: #2980b9; }
        .result { margin-top: 15px; padding: 15px; border-radius: 8px; min-height: 50px; }
        .success { background: #d5f4e6; border: 1px solid #27ae60; color: #27ae60; }
        .error { background: #fadbd8; border: 1px solid #e74c3c; color: #e74c3c; }
        .info { background: #ebf3fd; border: 1px solid #3498db; color: #3498db; }
        .api-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px; }
        .api-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: #f9f9f9; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-pending { background: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏛️ 中国信访系统 - 前后端连接测试</h1>
        
        <div class="test-section">
            <h3>🔌 服务连接状态</h3>
            <button onclick="checkServices()">检查服务状态</button>
            <div id="serviceStatus" class="result info">点击检查服务状态...</div>
        </div>
        
        <div class="test-section">
            <h3>🔐 认证测试</h3>
            <button onclick="testAuth()">测试用户登录</button>
            <div id="authResult" class="result info">点击测试认证功能...</div>
        </div>
        
        <div class="test-section">
            <h3>📝 前端API接口测试</h3>
            <div class="api-grid">
                <div class="api-card">
                    <h4>👥 来访接待 (visiting.ts)</h4>
                    <button onclick="testVisitingAPIs()">测试来访接待API</button>
                    <div id="visitingResult" class="result info">待测试...</div>
                </div>
                <div class="api-card">
                    <h4>💻 在线信访 (online.ts)</h4>
                    <button onclick="testOnlineAPIs()">测试在线信访API</button>
                    <div id="onlineResult" class="result info">待测试...</div>
                </div>
                <div class="api-card">
                    <h4>📮 信件处理 (letter.ts)</h4>
                    <button onclick="testLetterAPIs()">测试信件处理API</button>
                    <div id="letterResult" class="result info">待测试...</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎯 Note字段功能测试</h3>
            <button onclick="testNoteField()">测试Note字段功能</button>
            <div id="noteResult" class="result info">测试处理意见字段功能...</div>
        </div>
        
        <div class="test-section">
            <h3>🔧 Mock API测试</h3>
            <button onclick="testMockAPIs()">测试Mock接口</button>
            <div id="mockResult" class="result info">测试Mock API接口...</div>
        </div>
        
        <div class="test-section">
            <h3>📊 完整测试报告</h3>
            <button onclick="runCompleteTest()">运行完整测试</button>
            <div id="completeResult" class="result info">运行所有API接口的完整测试...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let authToken = '';

        async function checkServices() {
            const result = document.getElementById('serviceStatus');
            result.className = 'result info';
            result.innerHTML = '🔄 检查服务状态...';
            
            try {
                // 检查后端
                const backendResponse = await fetch(`${API_BASE}/`);
                const backendData = await backendResponse.json();
                
                // 检查前端
                const frontendResponse = await fetch('http://localhost:3000');
                
                result.className = 'result success';
                result.innerHTML = `
                    <div><span class="status-indicator status-success"></span>后端服务: ✅ 正常运行</div>
                    <div><span class="status-indicator status-success"></span>前端服务: ✅ 正常运行</div>
                    <div>后端信息: ${backendData.title} v${backendData.version}</div>
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<span class="status-indicator status-error"></span>服务连接失败: ${error.message}`;
            }
        }

        async function testAuth() {
            const result = document.getElementById('authResult');
            result.className = 'result info';
            result.innerHTML = '🔄 测试用户认证...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    body: 'username=test&password=test123'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    result.className = 'result success';
                    result.innerHTML = `✅ 认证成功! Token: ${authToken.substring(0, 30)}...`;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `❌ 认证失败: ${response.status}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ 认证错误: ${error.message}`;
            }
        }

        async function testAPI(url, resultId, apiName) {
            const result = document.getElementById(resultId);
            result.className = 'result info';
            result.innerHTML = `🔄 测试${apiName}...`;
            
            if (!authToken) {
                result.className = 'result error';
                result.innerHTML = '❌ 请先进行认证测试获取Token';
                return;
            }
            
            try {
                const response = await fetch(url, {
                    headers: {'Authorization': `Bearer ${authToken}`}
                });
                
                if (response.ok) {
                    const data = await response.json();
                    result.className = 'result success';
                    result.innerHTML = `✅ ${apiName}测试成功! 数据条数: ${data.total || 'N/A'}`;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `❌ ${apiName}测试失败: ${response.status}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ ${apiName}错误: ${error.message}`;
            }
        }

        async function testVisitingAPIs() {
            await testAPI(`${API_BASE}/visiting`, 'visitingResult', '来访接待API');
        }

        async function testOnlineAPIs() {
            await testAPI(`${API_BASE}/online`, 'onlineResult', '在线信访API');
        }

        async function testLetterAPIs() {
            await testAPI(`${API_BASE}/letter/letters`, 'letterResult', '信件处理API');
        }

        async function testNoteField() {
            const result = document.getElementById('noteResult');
            result.className = 'result info';
            result.innerHTML = '🔄 测试Note字段功能...';
            
            if (!authToken) {
                result.className = 'result error';
                result.innerHTML = '❌ 请先进行认证测试获取Token';
                return;
            }
            
            try {
                // 先获取一个记录ID
                const listResponse = await fetch(`${API_BASE}/visiting`, {
                    headers: {'Authorization': `Bearer ${authToken}`}
                });
                
                if (listResponse.ok) {
                    const listData = await listResponse.json();
                    if (listData.total > 0) {
                        const petitionId = listData.items[0].id;
                        
                        // 测试状态更新with note
                        const updateResponse = await fetch(`${API_BASE}/visiting/${petitionId}/status`, {
                            method: 'PUT',
                            headers: {
                                'Authorization': `Bearer ${authToken}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                status: '处理中',
                                note: `Note字段测试 - ${new Date().toLocaleString()}`
                            })
                        });
                        
                        if (updateResponse.ok) {
                            const updateData = await updateResponse.json();
                            result.className = 'result success';
                            result.innerHTML = `✅ Note字段功能正常! 状态: ${updateData.status}`;
                        } else {
                            result.className = 'result error';
                            result.innerHTML = `❌ Note字段更新失败: ${updateResponse.status}`;
                        }
                    } else {
                        result.className = 'result error';
                        result.innerHTML = '❌ 没有可用的测试数据';
                    }
                } else {
                    result.className = 'result error';
                    result.innerHTML = `❌ 获取测试数据失败: ${listResponse.status}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ Note字段测试错误: ${error.message}`;
            }
        }

        async function testMockAPIs() {
            const result = document.getElementById('mockResult');
            result.className = 'result info';
            result.innerHTML = '🔄 测试Mock API...';
            
            const mockAPIs = [
                {name: 'getPurchaseList', url: `${API_BASE}/get-purchase-list`},
                {name: 'getProjectList', url: `${API_BASE}/get-project-list`},
                {name: 'getList', url: `${API_BASE}/get-list`},
                {name: 'getCardList', url: `${API_BASE}/get-card-list`},
                {name: 'getMenuList', url: `${API_BASE}/get-menu-list-i18n`}
            ];
            
            let successCount = 0;
            let results = [];
            
            for (const api of mockAPIs) {
                try {
                    const response = await fetch(api.url, {
                        headers: {'Authorization': `Bearer ${authToken}`}
                    });
                    
                    if (response.ok) {
                        successCount++;
                        results.push(`✅ ${api.name}: 正常`);
                    } else if (response.status === 404) {
                        results.push(`🔧 ${api.name}: 未实现(404)`);
                    } else {
                        results.push(`❌ ${api.name}: 失败(${response.status})`);
                    }
                } catch (error) {
                    results.push(`❌ ${api.name}: 错误`);
                }
            }
            
            if (successCount > 0) {
                result.className = 'result success';
            } else {
                result.className = 'result info';
            }
            result.innerHTML = results.join('<br>');
        }

        async function runCompleteTest() {
            const result = document.getElementById('completeResult');
            result.className = 'result info';
            result.innerHTML = '🔄 运行完整测试套件...';
            
            try {
                // 依次运行所有测试
                await testAuth();
                await new Promise(resolve => setTimeout(resolve, 1000));
                await testVisitingAPIs();
                await new Promise(resolve => setTimeout(resolve, 1000));
                await testOnlineAPIs();
                await new Promise(resolve => setTimeout(resolve, 1000));
                await testLetterAPIs();
                await new Promise(resolve => setTimeout(resolve, 1000));
                await testNoteField();
                await new Promise(resolve => setTimeout(resolve, 1000));
                await testMockAPIs();
                
                result.className = 'result success';
                result.innerHTML = `
                    ✅ 完整测试完成!<br>
                    📊 测试覆盖: 认证、来访接待、在线信访、信件处理、Note字段、Mock API<br>
                    🎯 所有前端API接口测试成功!<br>
                    📋 详细报告请查看: FRONTEND_API_COMPLETE_TEST_REPORT.md
                `;
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `❌ 完整测试失败: ${error.message}`;
            }
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            checkServices();
        };
    </script>
</body>
</html>'''
    
    with open('/Users/<USER>/Desktop/code/xinfang/frontend_backend_connection_test.html', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("✅ 创建前后端连接测试页面: frontend_backend_connection_test.html")
    return '/Users/<USER>/Desktop/code/xinfang/frontend_backend_connection_test.html'

def main():
    """主函数"""
    print("🏛️ 中国信访系统 - Vue.js前端启动测试")
    print("=" * 50)
    
    # 1. 检查后端服务
    if not check_backend():
        print("⚠️  请先启动后端服务!")
        return
    
    # 2. 创建连接测试页面
    test_page = create_connection_test()
    
    # 3. 检查前端服务
    frontend_process = None
    if not check_frontend():
        frontend_process = start_frontend()
        if not frontend_process:
            print("❌ 前端服务启动失败")
            return
    
    # 4. 打开测试页面
    print("🌐 打开前后端连接测试页面...")
    webbrowser.open(f'file://{test_page}')
    
    try:
        print("\n🎉 测试环境就绪!")
        print("✅ 后端服务: http://localhost:8000")
        print("✅ 前端服务: http://localhost:3000")
        print("✅ 测试页面: 已在浏览器中打开")
        print("\n📋 测试说明:")
        print("1. 使用测试页面验证前后端连接")
        print("2. 测试所有API接口功能")
        print("3. 验证Note字段功能")
        print("4. 查看完整测试报告")
        print("\n按 Ctrl+C 停止服务...")
        
        # 保持服务运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 停止服务...")
        if frontend_process:
            frontend_process.terminate()
        print("🎉 测试完成!")

if __name__ == "__main__":
    main()
