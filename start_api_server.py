#!/usr/bin/env python3
"""
中国信访系统后端API服务启动器
自动启动FastAPI服务并显示文档链接
"""
import subprocess
import sys
import os
import time
import webbrowser
from pathlib import Path

def print_colored(text, color_code):
    """打印彩色文本"""
    print(f"\033[{color_code}m{text}\033[0m")

def print_header(text):
    print_colored(f"\n{'='*60}", "96")
    print_colored(f"{text}", "96;1")
    print_colored(f"{'='*60}", "96")

def print_success(text):
    print_colored(f"✅ {text}", "92")

def print_info(text):
    print_colored(f"ℹ️  {text}", "94")

def print_error(text):
    print_colored(f"❌ {text}", "91")

def check_python_env():
    """检查Python环境"""
    print_info("检查Python环境...")
    
    # 检查Python版本
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print_success(f"Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print_error(f"Python版本过低: {version.major}.{version.minor}.{version.micro}，需要3.8+")
        return False

def setup_environment():
    """设置环境"""
    api_server_path = "/Users/<USER>/Desktop/code/xinfang/api-server"
    
    print_info("切换到API服务器目录...")
    if not os.path.exists(api_server_path):
        print_error(f"API服务器目录不存在: {api_server_path}")
        return False
    
    os.chdir(api_server_path)
    print_success(f"已切换到: {os.getcwd()}")
    
    # 检查虚拟环境
    venv_path = os.path.join(api_server_path, "venv")
    if os.path.exists(venv_path):
        print_success("虚拟环境已存在")
    else:
        print_info("虚拟环境不存在，正在创建...")
        try:
            subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
            print_success("虚拟环境创建成功")
        except subprocess.CalledProcessError:
            print_error("虚拟环境创建失败")
            return False
    
    return True

def install_dependencies():
    """安装依赖"""
    print_info("检查并安装依赖...")
    
    # 确定Python可执行文件路径
    if os.name == 'nt':  # Windows
        python_exe = os.path.join("venv", "Scripts", "python.exe")
        pip_exe = os.path.join("venv", "Scripts", "pip.exe")
    else:  # Unix/macOS
        python_exe = os.path.join("venv", "bin", "python")
        pip_exe = os.path.join("venv", "bin", "pip")
    
    try:
        # 升级pip
        subprocess.run([pip_exe, "install", "--upgrade", "pip"], check=True, capture_output=True)
        
        # 安装依赖
        if os.path.exists("requirements.txt"):
            subprocess.run([pip_exe, "install", "-r", "requirements.txt"], check=True, capture_output=True)
            print_success("依赖安装完成")
        else:
            print_error("requirements.txt文件不存在")
            return False
            
    except subprocess.CalledProcessError as e:
        print_error(f"依赖安装失败: {e}")
        return False
    
    return True

def start_api_service():
    """启动API服务"""
    print_header("🚀 启动中国信访系统后端API服务")
    
    # 确定Python可执行文件路径
    if os.name == 'nt':  # Windows
        python_exe = os.path.join("venv", "Scripts", "python.exe")
    else:  # Unix/macOS
        python_exe = os.path.join("venv", "bin", "python")
    
    print_info("正在启动FastAPI服务...")
    
    # 显示访问信息
    print_header("📄 API文档和服务信息")
    print_colored("🌐 服务地址:", "95")
    print("   http://localhost:8000")
    print_colored("📚 API文档地址:", "95")
    print("   Swagger UI: http://localhost:8000/docs")
    print("   ReDoc:      http://localhost:8000/redoc")
    print("   OpenAPI:    http://localhost:8000/openapi.json")
    print_colored("🔗 API基础URL:", "95")
    print("   http://localhost:8000/api/v1")
    print_header("🎯 主要API端点")
    print("   认证: POST /api/v1/auth/login")
    print("   来访: GET  /api/v1/visiting")
    print("   网上: GET  /api/v1/online") 
    print("   信件: GET  /api/v1/letter/letters")
    
    try:
        # 启动服务
        print_info("服务启动中，请稍候...")
        process = subprocess.Popen([python_exe, "main.py"])
        
        # 等待服务启动
        time.sleep(3)
        
        print_success("服务启动成功！")
        print_info("按 Ctrl+C 停止服务")
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print_info("收到停止信号，正在关闭服务...")
        process.terminate()
        print_success("服务已停止")
    except Exception as e:
        print_error(f"启动服务失败: {e}")
        return False
    
    return True

def open_docs():
    """打开API文档页面"""
    print_info("是否要打开API文档页面？(y/n): ")
    try:
        choice = input().lower()
        if choice in ['y', 'yes', '是']:
            print_info("正在打开API文档...")
            webbrowser.open('http://localhost:8000/docs')
            time.sleep(1)
            webbrowser.open('http://localhost:8000/redoc')
    except:
        pass

def main():
    """主函数"""
    print_header("🏛️ 中国信访系统API服务启动器")
    
    # 1. 检查Python环境
    if not check_python_env():
        return False
    
    # 2. 设置环境
    if not setup_environment():
        return False
    
    # 3. 安装依赖
    if not install_dependencies():
        return False
    
    # 4. 启动服务
    if not start_api_service():
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print_success("API服务启动完成！")
        else:
            print_error("API服务启动失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print_info("用户取消操作")
    except Exception as e:
        print_error(f"启动过程中发生错误: {e}")
        sys.exit(1)
