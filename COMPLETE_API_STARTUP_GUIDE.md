# 🚀 中国信访系统API服务启动完整指南

## 📋 快速启动步骤

### 🎯 推荐方式：使用API控制面板
已为您打开 **API服务控制面板**，通过浏览器可以：
- ✅ 实时检查服务状态
- 📚 直接访问API文档
- 🧪 在线测试API接口
- 📋 复制启动命令

### 🖥️ 手动启动服务

#### 方法1：标准启动步骤
```bash
# 1. 打开终端，切换到API服务器目录
cd /Users/<USER>/Desktop/code/xinfang/api-server

# 2. 激活虚拟环境
source venv/bin/activate

# 3. 启动服务
python main.py
```

#### 方法2：使用启动脚本
```bash
# 切换到项目根目录
cd /Users/<USER>/Desktop/code/xinfang

# 运行直接启动脚本
python3 direct_start_api.py
```

## 📄 API文档访问地址

服务启动成功后，可通过以下地址访问：

### 🌟 核心文档页面
| 文档类型 | 地址 | 说明 |
|----------|------|------|
| **Swagger UI** | http://localhost:8000/docs | 🔥 **推荐** - 交互式API文档，可直接测试 |
| **ReDoc** | http://localhost:8000/redoc | 📖 美观的API文档展示 |
| **OpenAPI JSON** | http://localhost:8000/openapi.json | 🔧 API规范JSON格式 |

### 🔗 服务地址
- **主页**: http://localhost:8000
- **API基础URL**: http://localhost:8000/api/v1

## 🎯 主要API端点预览

### 🔐 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/test-token` - Token验证

### 👥 来访接待
- `GET /api/v1/visiting` - 获取来访列表
- `POST /api/v1/visiting` - 创建来访记录
- `GET /api/v1/visiting/{id}` - 获取来访详情
- `PUT /api/v1/visiting/{id}/status` - 更新状态
- `PUT /api/v1/visiting/{id}/assign` - 分配处理人

### 💻 网上信访
- `GET /api/v1/online` - 获取网上信访列表
- `POST /api/v1/online` - 创建网上信访
- `GET /api/v1/online/{id}` - 获取详情
- `PUT /api/v1/online/{id}/status` - 更新状态
- `PUT /api/v1/online/{id}/assign` - 分配处理人

### 📮 信件处理
- `GET /api/v1/letter/letters` - 获取信件列表
- `POST /api/v1/letter/letters` - 创建信件记录
- `GET /api/v1/letter/letters/{id}` - 获取详情
- `PUT /api/v1/letter/letters/{id}/status` - 更新状态
- `PUT /api/v1/letter/letters/{id}/assign` - 分配处理人

## 🧪 测试API功能

### 1. 使用Swagger UI（推荐）
1. 访问 http://localhost:8000/docs
2. 展开任意API端点
3. 点击 "Try it out" 按钮
4. 填写参数并点击 "Execute"

### 2. 使用完整测试脚本
```bash
cd /Users/<USER>/Desktop/code/xinfang
python3 complete_frontend_api_test.py
```

### 3. 手动curl测试
```bash
# 测试登录
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=test&password=test123"

# 测试获取来访列表
curl -X GET "http://localhost:8000/api/v1/visiting" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🔧 故障排除

### ❓ 端口被占用
如果8000端口被占用，可以修改端口：
```python
# 在 main.py 中修改
uvicorn.run("main:app", host="0.0.0.0", port=8001, reload=True)
```

### ❓ 依赖包问题
重新安装依赖：
```bash
cd /Users/<USER>/Desktop/code/xinfang/api-server
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### ❓ 虚拟环境问题
重新创建虚拟环境：
```bash
cd /Users/<USER>/Desktop/code/xinfang/api-server
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

## ✅ 服务启动成功标志

看到以下信息表示服务启动成功：
```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [xxxxx] using StatReload
INFO:     Started server process [xxxxx]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
```

## 🎉 API功能特性

✅ **FastAPI框架** - 高性能异步API  
✅ **自动文档生成** - Swagger UI + ReDoc  
✅ **JWT认证授权** - 安全的用户认证  
✅ **数据验证** - Pydantic模型验证  
✅ **CORS支持** - 跨域请求支持  
✅ **SQLAlchemy ORM** - 数据库操作  
✅ **错误处理** - 标准HTTP状态码  
✅ **开发热重载** - 代码修改自动重启  

---

## 🏆 下一步操作

1. **启动API服务** - 使用上述方法之一启动服务
2. **查看API文档** - 访问 http://localhost:8000/docs
3. **测试API接口** - 使用Swagger UI进行交互测试
4. **运行完整测试** - 执行前端API测试脚本
5. **开发调试** - 服务支持热重载，可直接修改代码

**🎊 API服务启动指南完成！现在您可以开始使用中国信访系统的后端API服务了！**
