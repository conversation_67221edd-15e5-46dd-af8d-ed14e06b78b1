.container {
  width: 100%;
  height: 100%;
  background-color: #edf0f3;
  padding: 20px;
  overflow-y: scroll;
  -ms-overflow-style: none;

  /* IE and Edge */
  scrollbar-width: none;

  /* Firefox */
}

.container::-webkit-scrollbar {
  display: none;
}

.t-table .t-table__content .t-table__header tr {
  background: rgb(210 213 225 / 57%);
}

.t-table .t-table__content .t-table__header tr th {
  border-left: none;
}

.t-table .t-table__content .t-table__header tr th .t-table__th-cell-inner {
  font-weight: 500;
  font-size: 14px;
  color: #353637;
}

.t-table .t-table__content .t-table__body tr td {
  border-left: none;
  font-size: 14px;
  color: #242f57;
  line-height: 24px;
}

.t-button--theme-primary {
  background-color: #2a5caa !important;
  border-color: #2a5caa !important;
}

.t-button--theme-danger {
  background-color: #dd1f1f !important;
  border-color: #dd1f1f !important;
}

.t-button--theme-warning {
  background-color: #df9139 !important;
  border-color: #df9139 !important;
}

.t-input-number,
.t-input-adornment {
  width: 100%;
}

.t-dialog {
  padding: 0;
  border-radius: 4px;
  border: none;
  overflow: hidden;
}

.t-dialog .t-dialog__header {
  padding: 0 20px;
  height: 56px;
  background: #2a5caa;
}

.t-dialog .t-dialog__header .t-dialog__header-content {
  font-weight: 600;
  font-size: 20px;
  color: #fff;
  line-height: 28px;
}

.t-dialog .t-dialog__header .t-dialog__close {
  width: 32px;
  color: #fff;
}

.t-dialog .t-dialog__header .t-dialog__close:hover {
  background: none;
}

.t-dialog .t-dialog__header .t-dialog__close .t-icon {
  font-size: 32px;
}

.t-dialog .t-dialog__body {
  padding: 20px;
}

.t-dialog .t-dialog__footer {
  border-top: 1px solid #ededed;
  padding: 20px;
}
