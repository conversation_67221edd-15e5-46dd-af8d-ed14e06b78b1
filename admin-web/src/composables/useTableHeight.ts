import { onMounted, onUnmounted, ref } from 'vue';

export default function useTableHeight() {
  const tableHeight = ref(0);

  const updateTableHeight = () => {
    const tableEl = document.querySelector('.t-table-wrapper');
    const paginationEl = document.querySelector('.t-table__pagination-wrap');
    if (tableEl && paginationEl) {
      tableHeight.value = tableEl.clientHeight - paginationEl.clientHeight;
    }
  };

  onMounted(() => {
    updateTableHeight();
    window.addEventListener('resize', updateTableHeight);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', updateTableHeight);
  });

  return {
    tableHeight,
  };
}
