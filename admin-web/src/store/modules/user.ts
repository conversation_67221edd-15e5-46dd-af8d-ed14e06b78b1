import { defineStore } from 'pinia';

import { usePermissionStore } from '@/store';
import { login, getCurrentUserInfo, testToken } from '@/api/auth';
import type { UserInfo as ApiUserInfo, LoginParams } from '@/api/auth';

// 兼容旧的UserInfo接口
interface UserInfo {
  name: string;
  roles: string[];
  id?: number;
  username?: string;
  email?: string;
  full_name?: string;
  phone?: string;
  is_active?: boolean;
  is_superuser?: boolean;
}

const InitUserInfo: UserInfo = {
  name: '', // 用户名，用于展示在页面右上角头像处
  roles: [], // 前端权限模型使用 如果使用请配置modules/permission-fe.ts使用
};

export const useUserStore = defineStore('user', {
  state: () => ({
    token: '', // 认证token
    userInfo: { ...InitUserInfo },
  }),
  getters: {
    roles: (state) => {
      return state.userInfo?.roles;
    },
  },
  actions: {
    async onLogin(loginData: LoginParams) {
      try {
        const response = await login(loginData);
        this.token = response.access_token;

        // 登录成功后获取用户信息
        await this.getUserInfo();

        return {
          code: 200,
          message: '登录成功',
          data: response.access_token,
        };
      } catch (error) {
        console.error('Login failed:', error);
        throw error;
      }
    },

    async getUserInfo() {
      try {
        const userInfo: ApiUserInfo = await getCurrentUserInfo();

        // 转换API用户信息为store格式
        this.userInfo = {
          name: userInfo.full_name || userInfo.username,
          roles: userInfo.roles?.map(role => role.name) || [],
          id: userInfo.id,
          username: userInfo.username,
          email: userInfo.email,
          full_name: userInfo.full_name,
          phone: userInfo.phone,
          is_active: userInfo.is_active,
          is_superuser: userInfo.is_superuser,
        };

        return this.userInfo;
      } catch (error) {
        console.error('Get user info failed:', error);
        // 如果获取用户信息失败，清除token
        this.token = '';
        throw error;
      }
    },

    async checkToken() {
      if (!this.token) {
        return false;
      }

      try {
        await testToken();
        return true;
      } catch (error) {
        console.error('Token validation failed:', error);
        this.token = '';
        return false;
      }
    },

    async logout() {
      this.token = '';
      this.userInfo = { ...InitUserInfo };
    },
  },
  persist: {
    afterRestore: () => {
      const permissionStore = usePermissionStore();
      permissionStore.initRoutes();
    },
    key: 'user',
    paths: ['token'],
  },
});
