import { HomeFilledIcon } from 'tdesign-icons-vue-next';
import { shallowRef } from 'vue';

import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/homepage',
    component: Layout,
    name: 'homepage',
    meta: {
      title: '首页',
      icon: shallowRef(HomeFilledIcon),
      orderNo: 0,
    },
    children: [
      {
        path: '',
        name: 'Homepage',
        component: () => import('@/pages/homepage/index.vue'),
      },
    ],
  },
];
