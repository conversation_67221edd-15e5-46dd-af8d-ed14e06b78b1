import { SunFallFilledIcon } from 'tdesign-icons-vue-next';
import { shallowRef } from 'vue';

import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/daily-updates',
    component: Layout,
    name: 'daily-updates',
    meta: {
      title: '每日动态',
      icon: shallowRef(SunFallFilledIcon),
      orderNo: 7,
    },
    children: [
      {
        path: '',
        name: 'DailyUpdates',
        component: () => import('@/pages/daily-updates/index.vue'),
      },
    ],
  },
];
