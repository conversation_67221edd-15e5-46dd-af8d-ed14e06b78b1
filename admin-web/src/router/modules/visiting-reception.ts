import { UndertakeHoldUpFilledIcon } from 'tdesign-icons-vue-next';
import { shallowRef } from 'vue';

import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/visiting-reception',
    component: Layout,
    name: 'visiting-reception',
    meta: {
      title: '来访接待',
      icon: shallowRef(UndertakeHoldUpFilledIcon),
      orderNo: 1,
    },
    children: [
      {
        path: '',
        name: 'VisitingReception',
        component: () => import('@/pages/visiting-reception/index.vue'),
      },
      {
        path: 'create',
        name: 'VisitingReceptionCreate',
        component: () => import('@/pages/visiting-reception/create.vue'),
      },
      {
        path: 'detail',
        name: 'VisitingReceptionDetail',
        component: () => import('@/pages/visiting-reception/detail.vue'),
      },
    ],
  },
];
