import { ChartAnalyticsIcon } from 'tdesign-icons-vue-next';
import { shallowRef } from 'vue';

import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/data-analysis',
    component: Layout,
    name: 'data-analysis',
    meta: {
      title: '数据分析',
      icon: shallowRef(ChartAnalyticsIcon),
      orderNo: 6,
    },
    children: [
      {
        path: '',
        name: 'DataAnalysis',
        component: () => import('@/pages/result/developing/index.vue'),
      },
    ],
  },
];
