import { DataSearchIcon } from 'tdesign-icons-vue-next';
import { shallowRef } from 'vue';

import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/information-retrieval',
    component: Layout,
    name: 'information-retrieval',
    meta: {
      title: '信息检索',
      icon: shallowRef(DataSearchIcon),
      orderNo: 3,
    },
    children: [
      {
        path: '',
        name: 'InformationRetrieval',
        component: () => import('@/pages/result/developing/index.vue'),
      },
    ],
  },
];
