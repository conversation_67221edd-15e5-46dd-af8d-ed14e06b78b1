<template>
  <div :class="layoutCls">
    <t-head-menu :class="menuCls" :theme="menuTheme" expand-type="popup" :value="active">
      <template #logo>
        <div class="header-logo-container" @click="handleNav('/')">
          <img src="@/assets/images/logo.png" alt="" />
          <span>公安信访智能化系统</span>
        </div>
      </template>
      <template v-if="layout !== 'side'" #default>
        <menu-content class="header-menu" :nav-data="menu" />
      </template>
      <template #operations>
        <img v-if="userStore.userInfo?.name" :src="userStore.userInfo.name" alt="" />
        <div v-else>
          <user-circle-filled-icon font-size="42px" color="#ffffff" />
        </div>
        <t-space separator="|">
          <span>用户名</span>
          <span>管理员</span>
        </t-space>
        <logout-icon font-size="22px" color="#ffffff" />
      </template>
    </t-head-menu>
  </div>
</template>

<script setup lang="ts">
import { LogoutIcon, UserCircleFilledIcon } from 'tdesign-icons-vue-next';
import type { PropType } from 'vue';
import { computed } from 'vue';
import { useRouter } from 'vue-router';

import { prefix } from '@/config/global';
import { getActive } from '@/router';
import { useUserStore } from '@/store';
import type { MenuRoute, ModeType } from '@/types/interface';

import MenuContent from './MenuContent.vue';

const { theme, layout, menu, isFixed, isCompact } = defineProps({
  theme: {
    type: String,
    default: 'light',
  },
  layout: {
    type: String,
    default: 'top',
  },
  showLogo: {
    type: Boolean,
    default: true,
  },
  menu: {
    type: Array as PropType<MenuRoute[]>,
    default: (): MenuRoute[] => [],
  },
  isFixed: {
    type: Boolean,
    default: false,
  },
  isCompact: {
    type: Boolean,
    default: false,
  },
  maxLevel: {
    type: Number,
    default: 3,
  },
});

const router = useRouter();
const userStore = useUserStore();
const active = computed(() => getActive());

const layoutCls = computed(() => [`${prefix}-header-layout`]);

const menuCls = computed(() => {
  return [
    {
      [`${prefix}-header-menu`]: !isFixed,
      [`${prefix}-header-menu-fixed`]: isFixed,
      [`${prefix}-header-menu-fixed-side`]: layout === 'side' && isFixed,
      [`${prefix}-header-menu-fixed-side-compact`]: layout === 'side' && isFixed && isCompact,
    },
  ];
});
const menuTheme = computed(() => theme as ModeType);

const handleNav = (url: string) => {
  router.push(url);
};

// const handleLogout = () => {
//   router.push({
//     path: '/login',
//     query: { redirect: encodeURIComponent(router.currentRoute.value.fullPath) },
//   });
// };
</script>
<style lang="less" scoped>
.@{starter-prefix}-header {
  &-menu-fixed {
    position: fixed;
    top: 0;
    z-index: 1001;

    :deep(.t-head-menu__inner) {
      padding-right: var(--td-comp-margin-xl);
    }

    &-side {
      left: 232px;
      right: 0;
      z-index: 10;
      width: auto;
      transition: all 0.3s;

      &-compact {
        left: 64px;
      }
    }
  }

  &-logo-container {
    cursor: pointer;
    display: inline-flex;
  }
}

.header-menu {
  flex: 1 1 1;
  display: inline-flex;

  :deep(.t-menu__item) {
    min-width: unset;
  }
}

.header-logo-container {
  // width: 324px;
  height: 100%;
  margin-left: 30px;
  margin-right: 76px;
  display: flex;
  align-items: center;
  column-gap: 13px;

  img {
    width: 27.84px;
    height: 30px;
  }

  span {
    font-size: 18px;
    color: #fff;
    line-height: 21px;
  }
}
</style>
