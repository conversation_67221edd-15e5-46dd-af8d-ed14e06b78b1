<script lang="ts" setup>
import { TableProps } from 'tdesign-vue-next';
import { ref } from 'vue';

import DistributionChart from './charts/distribution.vue';

const tableData = ref<TableProps['data']>([]);
const tableColumns = ref<TableProps['columns']>([
  {
    colKey: 'index',
    title: '序号',
    align: 'center',
    width: 80,
  },
  {
    colKey: 'receptionNo',
    title: '事项编号',
    align: 'center',
    width: 120,
  },
  {
    colKey: 'appealType',
    title: '事项类型',
    align: 'center',
    width: 120,
  },
  {
    colKey: 'visitor',
    title: '申请人',
    align: 'center',
    width: 100,
  },
  {
    colKey: 'visitTime',
    title: '申请时间',
    align: 'center',
    width: 160,
  },
  {
    colKey: 'region',
    title: '所属地区',
    align: 'center',
    width: 120,
  },
  {
    colKey: 'status',
    title: '处理状态',
    align: 'center',
    width: 100,
  },
  {
    colKey: 'operation',
    title: '操作',
    align: 'center',
    width: 100,
    fixed: 'right',
    cell: (h, { row }) => {
      return h(
        't-button',
        {
          variant: 'text',
          theme: 'primary',
          onClick: () => handleDetail(row),
        },
        '详情',
      );
    },
  },
]);

const tableData2 = ref<TableProps['data']>([]);
const tableColumns2 = ref<TableProps['columns']>([
  {
    colKey: 'index',
    title: '序号',
    align: 'center',
    width: 80,
  },
  {
    colKey: 'reportNo',
    title: '报告编号',
    align: 'center',
    width: 120,
  },
  {
    colKey: 'reportTime',
    title: '报告时间',
    align: 'center',
    width: 160,
  },
  {
    colKey: 'reportType',
    title: '报告类型',
    align: 'center',
    width: 120,
  },
  {
    colKey: 'status',
    title: '状态',
    align: 'center',
    width: 100,
  },
  {
    colKey: 'operation',
    title: '操作',
    align: 'center',
    width: 100,
    fixed: 'right',
    cell: (h, { row }) => {
      return h(
        't-button',
        {
          variant: 'outline',
          theme: 'primary',
          onClick: () => handleDetail(row),
        },
        '详情',
      );
    },
  },
]);

const riskLevels = [
  {
    value: 1,
    label: '中度',
    color: '#DF9139',
    backgroundColor: '#FFF6E7',
  },
  {
    value: 2,
    label: '高度',
    color: '#DD1F1F',
    backgroundColor: '#FFF0F0',
  },
];

const riskWarnings = ref([
  {
    title: '西区安置房问题可能引发集体上访',
    content:
      '近期西区安置房质量问题投诉频繁，涉及小区居民正在组织联名信访。建议住建部门提前介入调查，与居民代表沟通，了解具体问题并制定解决方案。',
    level: 1,
    dateRange: '本周内',
    scale: '30-50人',
  },
  {
    title: '西区安置房问题可能引发集体上访',
    content:
      '近期西区安置房质量问题投诉频繁，涉及小区居民正在组织联名信访。建议住建部门提前介入调查，与居民代表沟通，了解具体问题并制定解决方案。',
    level: 2,
    dateRange: '本周内',
    scale: '30-50人',
  },
]);

const workSuggestions = ref([
  {
    title: '1、加强城市建设类信访件处理力度',
    content:
      '针对占比最高的城市建设类信访问题，建议住建、城管等部门联动处理，成立专项工作组，优先解决噪音扰民、工程质量等群众反映强烈的问题。',
  },
  {
    title: '1、加强城市建设类信访件处理力度',
    content:
      '针对占比最高的城市建设类信访问题，建议住建、城管等部门联动处理，成立专项工作组，优先解决噪音扰民、工程质量等群众反映强烈的问题。',
  },
  {
    title: '1、加强城市建设类信访件处理力度',
    content:
      '针对占比最高的城市建设类信访问题，建议住建、城管等部门联动处理，成立专项工作组，优先解决噪音扰民、工程质量等群众反映强烈的问题。',
  },
  {
    title: '1、加强城市建设类信访件处理力度',
    content:
      '针对占比最高的城市建设类信访问题，建议住建、城管等部门联动处理，成立专项工作组，优先解决噪音扰民、工程质量等群众反映强烈的问题。',
  },
]);

const handleDetail = (row: any) => {
  // 处理详情操作逻辑
  console.log('查看详情:', row);
};
</script>

<template>
  <div class="container">
    <div class="breadcrumb">
      <img src="@/assets/images/homepage-nav.png" alt="" />
      <span>每日动态</span>
    </div>
    <div class="wrapper">
      <!-- 标题等信息 -->
      <div class="head">
        <div class="left">
          <img src="@/assets/images/daily-updates.png" alt="" />
          <div class="title">每日信访动态（2025）第67期</div>
          <div class="subtitle">文号：信办字〔2025〕67</div>
        </div>
        <div class="right">
          <div class="time">发布时间：2025年5月7日 08:30</div>
          <div class="editor">编辑：信访数据中心</div>
          <div class="status">状态：已分发</div>
        </div>
      </div>
      <!-- 一、信访总体情况 -->
      <div class="layer_1">
        <div class="title">一、信访总体情况</div>
        <div class="content">
          2025年5月6日，全市共接收信访件68件，其中来访接待28件，信件处理35件，网络信访5件。较上周同期下降12%，较上月同期上升8%。
        </div>
        <div class="statistics">
          <div class="item">
            <div class="left">
              <img src="@/assets/images/daily-updates-icon1.png" alt="" />
              <div>
                <div class="label">总信访量</div>
                <div class="number">68</div>
              </div>
            </div>
            <div class="right">
              <div class="rate">-12%</div>
            </div>
          </div>
          <div class="item">
            <div class="left">
              <img src="@/assets/images/daily-updates-icon2.png" alt="" />
              <div>
                <div class="label">来访接待</div>
                <div class="number">68</div>
              </div>
            </div>
            <div class="right">
              <div class="rate">-12%</div>
            </div>
          </div>
          <div class="item">
            <div class="left">
              <img src="@/assets/images/daily-updates-icon3.png" alt="" />
              <div>
                <div class="label">信件处理</div>
                <div class="number">28</div>
              </div>
            </div>
            <div class="right">
              <div class="rate">-12%</div>
            </div>
          </div>
          <div class="item">
            <div class="left">
              <img src="@/assets/images/daily-updates-icon4.png" alt="" />
              <div>
                <div class="label">网络信访</div>
                <div class="number">68</div>
              </div>
            </div>
            <div class="right">
              <div class="rate">-12%</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 二、重点事项与分布 -->
      <div class="layer_2">
        <div class="title">二、重点事项与分布</div>
        <div class="layer-wrapper">
          <div>
            <div class="content">
              昨日信访事项主要集中在城市建设、环境保护和社会福利三类问题，其中城市建设类占比42%，环境保护类占比28%，社会保障类占比18%，其他类别占比12%。
            </div>
            <div class="chart">
              <distribution-chart />
            </div>
          </div>
          <div>
            <div class="content">
              地区分布方面，北城区23件，占比34%；东城区18件，占比26%；西城区15件，占比22%；南城区12件，占比18%。
            </div>
            <div class="chart"></div>
          </div>
        </div>
      </div>
      <!-- 三、突出问题与处理进展 -->
      <div class="layer_3">
        <div class="title">二、重点事项与分布</div>
        <div class="t-table-wrapper">
          <t-table row-key="index" bordered :max-height="400" :data="tableData" :columns="tableColumns"></t-table>
        </div>
      </div>
      <!-- 四、风险预警与建议 -->
      <div class="layer_4">
        <div class="title">四、风险预警与建议</div>
        <div class="list">
          <div
            v-for="(item, index) in riskWarnings"
            :key="index"
            :style="{
              borderColor: riskLevels.find((level) => level.value === item.level)?.color,
              backgroundColor: riskLevels.find((level) => level.value === item.level)?.backgroundColor,
            }"
          >
            <div class="item-title">{{ item.title }}</div>
            <div class="item-content">{{ item.content }}</div>
            <div class="box">
              <div class="level">
                风险等级：<span
                  :style="{
                    color: riskLevels.find((level) => level.value === item.level)?.color,
                  }"
                  >{{ riskLevels.find((level) => level.value === item.level)?.label }}</span
                >
              </div>
              <div>预计时间段：{{ item.dateRange }}</div>
              <div>可能规模：{{ item.scale }}</div>
            </div>
            <div class="handle">
              <t-button v-if="item.level === 1" theme="primary" @click="handleDetail(item)">详细分析</t-button>
              <t-button v-if="item.level === 2" theme="danger" @click="handleDetail(item)">紧急处理</t-button>
              <t-button theme="primary" @click="handleDetail(item)">应对预案</t-button>
            </div>
            <div
              class="tip"
              :style="{
                backgroundColor: riskLevels.find((level) => level.value === item.level)?.color,
              }"
            >
              风险等级：<span>{{ riskLevels.find((level) => level.value === item.level)?.label }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 五、工作建议 -->
      <div class="layer_5">
        <div class="title">五、工作建议</div>
        <div class="list">
          <div v-for="(item, index) in workSuggestions" :key="index">
            <div class="item-title">{{ item.title }}</div>
            <div class="item-content">{{ item.content }}</div>
          </div>
        </div>
      </div>
      <!-- 操作 -->
      <div class="layer_6">
        <div class="left">
          <span>签发人：苦根</span>
          <span>审核：李副局长</span>
        </div>
        <div class="right">
          <t-button theme="primary" @click="handleDetail">导出PDF</t-button>
          <t-button theme="primary" @click="handleDetail">打印报告</t-button>
          <t-button theme="primary" @click="handleDetail">分发报告</t-button>
        </div>
      </div>
    </div>
    <div class="history">
      <div class="head">
        <img src="@/assets/images/homepage-schedule.png" alt="" />
        <span>历史报告</span>
      </div>
      <div class="layer">
        <div class="t-table-wrapper">
          <t-table row-key="index" bordered :max-height="400" :data="tableData2" :columns="tableColumns2"></t-table>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 10px;

  .breadcrumb {
    width: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    column-gap: 9px;

    img {
      width: 11px;
      height: 14px;
    }

    span {
      font-size: 14px;
      color: #242f57;
    }
  }

  .wrapper {
    width: 100%;
    background: #fff;
    box-shadow: 0 2px 4px 0 rgb(28 41 90 / 4%);
    border-radius: 4px;
    border: 1px solid #eaedf7;
    padding: 30px;

    .head {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .left {
        display: flex;
        align-items: center;

        img {
          width: 18px;
          height: 18px;
        }

        .title {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 28px;
          margin-left: 10px;
        }

        .subtitle {
          font-size: 14px;
          color: #242f57;
          line-height: 20px;
          margin-left: 40px;
        }
      }

      .right {
        display: flex;
        align-items: center;
        column-gap: 26px;

        > div {
          font-size: 14px;
          color: #242f57;
          line-height: 20px;
        }
      }
    }

    .layer_1 {
      width: 100%;
      padding: 20px 30px;
      border-bottom: 1px solid #c9d3de;

      .title {
        font-weight: 500;
        font-size: 16px;
        color: #242f57;
        line-height: 22px;
      }

      .content {
        font-size: 14px;
        color: #242f57;
        line-height: 20px;
        margin-top: 20px;
      }

      .statistics {
        width: 100%;
        margin-top: 20px;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        column-gap: 20px;

        .item {
          padding: 17px 20px 17px 45px;
          background: #fff;
          box-shadow: 0 1px 6px 0 rgb(184 184 184 / 44%);
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .left {
            display: flex;
            align-items: center;
            column-gap: 20px;

            img {
              width: 60px;
              height: 60px;
            }

            > div {
              .label {
                font-size: 16px;
                color: #61666f;
              }

              .number {
                font-weight: bold;
                font-size: 34px;
                color: #242f57;
                line-height: 40px;
                margin-top: 10px;
              }
            }
          }

          .right {
            align-self: flex-end;

            .rate {
              font-weight: 600;
              font-size: 20px;
              color: #dd1f1f;
              line-height: 28px;
            }
          }
        }
      }
    }

    .layer_2 {
      width: 100%;
      padding: 20px 30px;
      border-bottom: 1px solid #c9d3de;

      .title {
        font-weight: 500;
        font-size: 16px;
        color: #242f57;
        line-height: 22px;
      }

      .layer-wrapper {
        width: 100%;
        margin-top: 20px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        column-gap: 30px;

        > div {
          height: 100%;
          display: flex;
          flex-direction: column;

          .content {
            flex: 1;
            font-size: 14px;
            color: #242f57;
            line-height: 20px;
          }

          .chart {
            width: 100%;
            height: 254px;
            margin-top: 20px;
            background: #f8f9fa;
          }
        }
      }
    }

    .layer_3 {
      width: 100%;
      padding: 20px 30px;
      border-bottom: 1px solid #c9d3de;

      .title {
        font-weight: 500;
        font-size: 16px;
        color: #242f57;
        line-height: 22px;
      }

      .t-table-wrapper {
        margin-top: 20px;
      }
    }

    .layer_4 {
      width: 100%;
      padding: 20px 30px;
      border-bottom: 1px solid #c9d3de;

      .title {
        font-weight: 500;
        font-size: 16px;
        color: #242f57;
        line-height: 22px;
      }

      .list {
        width: 100%;
        margin-top: 20px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;

        > div {
          width: 100%;
          padding: 20px;
          border-radius: 4px;
          border: 1px solid;
          position: relative;

          .item-title {
            font-weight: 500;
            font-size: 14px;
            color: #242f57;
            line-height: 20px;
          }

          .item-content {
            font-size: 14px;
            color: #242f57;
            line-height: 24px;
            text-align: justify;
            margin-top: 10px;
          }

          .box {
            width: 100%;
            margin-top: 10px;
            display: flex;
            align-items: center;
            column-gap: 60px;

            > div {
              font-size: 14px;
              color: #242f57;
              line-height: 24px;
              text-align: justify;
            }
          }

          .handle {
            width: 100%;
            margin-top: 10px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            column-gap: 10px;
          }

          .tip {
            position: absolute;
            top: 0;
            right: 0;
            padding: 5px 8px 5px 18px;
            border-radius: 0 0 0 20px;
            font-size: 14px;
            color: #fff;
            line-height: 20px;
          }
        }
      }
    }

    .layer_5 {
      width: 100%;
      padding: 20px 30px;
      border-bottom: 1px solid #c9d3de;

      .title {
        font-weight: 500;
        font-size: 16px;
        color: #242f57;
        line-height: 22px;
      }

      .list {
        width: 100%;
        margin-top: 20px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;

        > div {
          width: 100%;
          padding: 20px;
          background: #f8f9fa;
          border-radius: 4px;

          .item-title {
            font-weight: 500;
            font-size: 16px;
            color: #2a5caa;
            line-height: 22px;
          }

          .item-content {
            padding: 0 20px;
            font-size: 14px;
            color: #242f57;
            line-height: 24px;
            text-align: justify;
            margin-top: 10px;
          }
        }
      }
    }

    .layer_6 {
      width: 100%;
      padding: 20px 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .left {
        display: flex;
        align-items: center;
        column-gap: 200px;

        span {
          font-size: 14px;
          color: #242f57;
          line-height: 20px;
        }
      }

      .right {
        display: flex;
        align-items: center;
        column-gap: 10px;
      }
    }
  }

  .history {
    width: 100%;
    height: 100%;
    background: #fff;
    box-shadow: 0 2px 4px 0 rgb(28 41 90 / 4%);
    border-radius: 4px;
    border: 1px solid #eaedf7;
    padding: 30px;

    .head {
      display: flex;
      align-items: center;
      column-gap: 10px;

      span {
        font-weight: 500;
        font-size: 20px;
        color: #242f57;
        line-height: 30px;
      }
    }

    .layer {
      width: 100%;
      padding: 20px 30px;
      padding-bottom: 0;

      .title {
        font-weight: 500;
        font-size: 16px;
        color: #242f57;
        line-height: 22px;
      }
    }
  }
}
</style>
