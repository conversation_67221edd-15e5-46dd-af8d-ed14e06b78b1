<template>
  <div class="function-test-container">
    <h1>功能测试页面</h1>
    
    <div class="test-section">
      <h2>信件处理功能测试</h2>
      <div class="test-buttons">
        <t-button @click="testCreateLetter" :loading="createLoading">创建信件</t-button>
        <t-button @click="testGetLetters" :loading="listLoading">获取信件列表</t-button>
        <t-button @click="testUpdateStatus" :loading="statusLoading">更新状态</t-button>
        <t-button @click="testAssignHandler" :loading="assignLoading">分配处理人</t-button>
      </div>
      <div class="test-results">
        <pre>{{ letterTestResults }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>文件上传功能测试</h2>
      <div class="test-buttons">
        <t-upload
          multiple
          :auto-upload="false"
          :size-limit="{ size: 10, unit: 'MB' }"
          :max="3"
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
          @change="testFileUpload"
        >
          <t-button theme="primary">选择文件测试上传</t-button>
        </t-upload>
      </div>
      <div class="test-results">
        <pre>{{ uploadTestResults }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>用户管理功能测试</h2>
      <div class="test-buttons">
        <t-button @click="testGetUsers" :loading="usersLoading">获取用户列表</t-button>
        <t-button @click="testGetCurrentUser" :loading="currentUserLoading">获取当前用户</t-button>
      </div>
      <div class="test-results">
        <pre>{{ userTestResults }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>统计数据功能测试</h2>
      <div class="test-buttons">
        <t-button @click="testGetStatistics" :loading="statsLoading">获取统计数据</t-button>
        <t-button @click="testGetTodayUpdate" :loading="todayLoading">获取今日数据</t-button>
      </div>
      <div class="test-results">
        <pre>{{ statsTestResults }}</pre>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';

// API imports
import { createLetterPetition, getLetterPetitions, updateLetterPetitionStatus, assignLetterPetitionHandler } from '@/api/letter';
import { uploadMultipleFiles } from '@/api/upload';
import { getUsers, getCurrentUserInfo } from '@/api/auth';
import { getTodayUpdate, getCardList } from '@/api/statistics';

// Loading states
const createLoading = ref(false);
const listLoading = ref(false);
const statusLoading = ref(false);
const assignLoading = ref(false);
const usersLoading = ref(false);
const currentUserLoading = ref(false);
const statsLoading = ref(false);
const todayLoading = ref(false);

// Test results
const letterTestResults = ref('');
const uploadTestResults = ref('');
const userTestResults = ref('');
const statsTestResults = ref('');

// 测试创建信件
const testCreateLetter = async () => {
  createLoading.value = true;
  try {
    const testData = {
      type: 'LETTER',
      title: '测试信件标题',
      content: '这是一个测试信件的内容',
      petitioner_name: '测试用户',
      petitioner_phone: '13800138000',
      petitioner_id_card: '110101199001010001',
      petitioner_address: '北京市朝阳区测试地址',
      priority: 1,
      is_sensitive: false,
      letter_info: {
        received_date: new Date().toISOString().split('T')[0],
        letter_source: '邮寄',
        has_attachments: false,
      }
    };
    
    const result = await createLetterPetition(testData);
    letterTestResults.value = `创建信件成功:\n${JSON.stringify(result, null, 2)}`;
    MessagePlugin.success('创建信件测试成功');
  } catch (error) {
    letterTestResults.value = `创建信件失败:\n${error}`;
    MessagePlugin.error('创建信件测试失败');
  } finally {
    createLoading.value = false;
  }
};

// 测试获取信件列表
const testGetLetters = async () => {
  listLoading.value = true;
  try {
    const result = await getLetterPetitions({ limit: 5 });
    letterTestResults.value = `获取信件列表成功:\n${JSON.stringify(result, null, 2)}`;
    MessagePlugin.success('获取信件列表测试成功');
  } catch (error) {
    letterTestResults.value = `获取信件列表失败:\n${error}`;
    MessagePlugin.error('获取信件列表测试失败');
  } finally {
    listLoading.value = false;
  }
};

// 测试更新状态
const testUpdateStatus = async () => {
  statusLoading.value = true;
  try {
    // 假设有ID为1的信件
    const result = await updateLetterPetitionStatus(1, {
      status: 'PROCESSING',
      note: '测试状态更新'
    });
    letterTestResults.value = `更新状态成功:\n${JSON.stringify(result, null, 2)}`;
    MessagePlugin.success('更新状态测试成功');
  } catch (error) {
    letterTestResults.value = `更新状态失败:\n${error}`;
    MessagePlugin.error('更新状态测试失败');
  } finally {
    statusLoading.value = false;
  }
};

// 测试分配处理人
const testAssignHandler = async () => {
  assignLoading.value = true;
  try {
    // 假设有ID为1的信件和ID为1的用户
    const result = await assignLetterPetitionHandler(1, 1);
    letterTestResults.value = `分配处理人成功:\n${JSON.stringify(result, null, 2)}`;
    MessagePlugin.success('分配处理人测试成功');
  } catch (error) {
    letterTestResults.value = `分配处理人失败:\n${error}`;
    MessagePlugin.error('分配处理人测试失败');
  } finally {
    assignLoading.value = false;
  }
};

// 测试文件上传
const testFileUpload = async (files: File[]) => {
  if (files.length === 0) return;
  
  try {
    const result = await uploadMultipleFiles(files);
    uploadTestResults.value = `文件上传成功:\n${JSON.stringify(result, null, 2)}`;
    MessagePlugin.success('文件上传测试成功');
  } catch (error) {
    uploadTestResults.value = `文件上传失败:\n${error}`;
    MessagePlugin.error('文件上传测试失败');
  }
};

// 测试获取用户列表
const testGetUsers = async () => {
  usersLoading.value = true;
  try {
    const result = await getUsers();
    userTestResults.value = `获取用户列表成功:\n${JSON.stringify(result, null, 2)}`;
    MessagePlugin.success('获取用户列表测试成功');
  } catch (error) {
    userTestResults.value = `获取用户列表失败:\n${error}`;
    MessagePlugin.error('获取用户列表测试失败');
  } finally {
    usersLoading.value = false;
  }
};

// 测试获取当前用户
const testGetCurrentUser = async () => {
  currentUserLoading.value = true;
  try {
    const result = await getCurrentUserInfo();
    userTestResults.value = `获取当前用户成功:\n${JSON.stringify(result, null, 2)}`;
    MessagePlugin.success('获取当前用户测试成功');
  } catch (error) {
    userTestResults.value = `获取当前用户失败:\n${error}`;
    MessagePlugin.error('获取当前用户测试失败');
  } finally {
    currentUserLoading.value = false;
  }
};

// 测试获取统计数据
const testGetStatistics = async () => {
  statsLoading.value = true;
  try {
    const result = await getCardList();
    statsTestResults.value = `获取统计数据成功:\n${JSON.stringify(result, null, 2)}`;
    MessagePlugin.success('获取统计数据测试成功');
  } catch (error) {
    statsTestResults.value = `获取统计数据失败:\n${error}`;
    MessagePlugin.error('获取统计数据测试失败');
  } finally {
    statsLoading.value = false;
  }
};

// 测试获取今日数据
const testGetTodayUpdate = async () => {
  todayLoading.value = true;
  try {
    const result = await getTodayUpdate();
    statsTestResults.value = `获取今日数据成功:\n${JSON.stringify(result, null, 2)}`;
    MessagePlugin.success('获取今日数据测试成功');
  } catch (error) {
    statsTestResults.value = `获取今日数据失败:\n${error}`;
    MessagePlugin.error('获取今日数据测试失败');
  } finally {
    todayLoading.value = false;
  }
};
</script>

<style lang="less" scoped>
.function-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }

  .test-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;

    h2 {
      margin-bottom: 20px;
      color: #555;
      border-bottom: 2px solid #007bff;
      padding-bottom: 10px;
    }

    .test-buttons {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .test-results {
      max-height: 300px;
      overflow-y: auto;
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;

      pre {
        margin: 0;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
}
</style>
