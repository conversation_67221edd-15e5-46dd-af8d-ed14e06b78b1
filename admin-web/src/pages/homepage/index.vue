<script lang="ts" setup>
import { AlarmIcon } from 'tdesign-icons-vue-next';
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';

import DistributionChart from './charts/distribution.vue';
import TrendChart from './charts/trend.vue';
import { getTodayUpdate } from '@/api/statistics';
import { getPetitions } from '@/api/petitions';
import type { Petition } from '@/api/model/petitionModel';

// 统计数据
const statistics = ref({
  todayVisiting: 0,
  todayLetter: 0,
  pendingCount: 0,
  riskWarning: 0,
});

// 待办事项
const schedule = ref<Petition[]>([]);

// 获取今日统计数据
const fetchTodayStatistics = async () => {
  try {
    const todayData = await getTodayUpdate();
    statistics.value = {
      todayVisiting: todayData.visiting_petition_count,
      todayLetter: todayData.letter_petition_count,
      pendingCount: todayData.pending_petition_count,
      riskWarning: todayData.sensitive_petition_count,
    };
  } catch (error) {
    console.error('获取统计数据失败:', error);
    MessagePlugin.error('获取统计数据失败');
  }
};

// 获取待办事项（待处理的信访）
const fetchPendingPetitions = async () => {
  try {
    const response = await getPetitions({
      status: 'PENDING',
      limit: 4,
    });
    schedule.value = response.items;
  } catch (error) {
    console.error('获取待办事项失败:', error);
    MessagePlugin.error('获取待办事项失败');
  }
};

// 计算剩余天数
const calculateRemainingDays = (createdAt: string) => {
  const created = new Date(createdAt);
  const now = new Date();
  const diffTime = now.getTime() - created.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, 7 - diffDays); // 假设7天为处理期限
};

// 跳转到详情页面
const handleGotoDetail = (item: Petition) => {
  const routeMap = {
    LETTER: 'LetterProcessingDetail',
    VISITING: 'VisitingReceptionDetail',
    ONLINE: 'OnlinePetitionDetail',
  };

  const routeName = routeMap[item.type] || 'LetterProcessingDetail';
  // 这里需要导入router
  // router.push({ name: routeName, params: { id: item.id } });
  console.log('跳转到详情页面:', routeName, item.id);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchTodayStatistics();
  fetchPendingPetitions();
});
</script>
<template>
  <div class="container">
    <!-- 面包屑 -->
    <div class="breadcrumb">
      <img src="@/assets/images/homepage-nav.png" alt="" />
      <span>首页</span>
    </div>
    <!-- 统计 -->
    <div class="statistics">
      <div class="statistic">
        <div class="name">
          <img src="@/assets/images/homepage-statistics1.png" alt="" />
          <span>今日来访接待量</span>
        </div>
        <span class="count">{{ statistics.todayVisiting }}</span>
      </div>
      <div class="statistic">
        <div class="name">
          <img src="@/assets/images/homepage-statistics2.png" alt="" />
          <span>今日信件处理量</span>
        </div>
        <span class="count">{{ statistics.todayLetter }}</span>
      </div>
      <div class="statistic">
        <div class="name">
          <img src="@/assets/images/homepage-statistics3.png" alt="" />
          <span>待处理信件</span>
        </div>
        <span class="count">{{ statistics.pendingCount }}</span>
      </div>
      <div class="statistic">
        <div class="name">
          <img src="@/assets/images/homepage-statistics4.png" alt="" />
          <span>风险预警</span>
        </div>
        <span class="count">{{ statistics.riskWarning }}</span>
      </div>
    </div>
    <div class="layer">
      <!-- 待办事项 -->
      <div class="schedules">
        <div class="title">
          <img src="@/assets/images/homepage-schedule.png" alt="" />
          <span>待办事项</span>
        </div>
        <div class="schedule">
          <div v-for="(item, index) in schedule" :key="item.id" class="item">
            <div class="left">
              <span>{{ item.title || '待处理信访事项' }}</span>
              <span>{{ item.type === 'LETTER' ? '信件处理' : item.type === 'VISITING' ? '来访接待' : '在线信访' }}</span>
            </div>
            <div class="right">
              <div class="endtime">
                <alarm-icon color="#DD1F1F" font-size="14px" />
                <span
                  >剩余<span style="color: #dd1f1f">{{ calculateRemainingDays(item.created_at) }}</span
                  >天</span
                >
              </div>
              <t-button theme="primary" @click="handleGotoDetail(item)">去处理</t-button>
            </div>
          </div>
          <div v-if="schedule.length === 0" class="empty">
            <span>暂无待办事项</span>
          </div>
        </div>
      </div>
      <!-- 核心功能 -->
      <div class="cores">
        <div class="title">
          <img src="@/assets/images/homepage-core.png" alt="" />
          <span>核心功能</span>
        </div>
        <div class="core">
          <div class="item">
            <img src="@/assets/images/homepage-core1.png" alt="" />
            <div>
              <span>来访接待智能化</span>
            </div>
          </div>
          <div class="item">
            <img src="@/assets/images/homepage-core1.png" alt="" />
            <div>
              <span>信件登记<br />处理自动化</span>
            </div>
          </div>
          <div class="item">
            <img src="@/assets/images/homepage-core1.png" alt="" />
            <div>
              <span>信息检索<br />与画像分析</span>
            </div>
          </div>
          <div class="item">
            <img src="@/assets/images/homepage-core1.png" alt="" />
            <div>
              <span>网络信访<br />服务优化</span>
            </div>
          </div>
          <div class="item">
            <img src="@/assets/images/homepage-core1.png" alt="" />
            <div>
              <span>数据治理<br />与风险预警</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 快速操作 -->
      <div class="quicks">
        <div class="title">
          <img src="@/assets/images/homepage-quick.png" alt="" />
          <span>核心功能</span>
        </div>
        <div class="quick">
          <div class="item">
            <img src="@/assets/images/homepage-quick1.png" alt="" />
            <span>新建来访接待</span>
          </div>
          <div class="item">
            <img src="@/assets/images/homepage-quick2.png" alt="" />
            <span>新建信件登记</span>
          </div>
          <div class="item">
            <img src="@/assets/images/homepage-quick3.png" alt="" />
            <span>信息检索</span>
          </div>
        </div>
      </div>
    </div>
    <div class="charts">
      <!-- 近期信访趋势 -->
      <div class="trend">
        <div class="title">
          <img src="@/assets/images/homepage-trend.png" alt="" />
          <span>近期信访趋势</span>
        </div>
        <div class="chart">
          <trend-chart />
        </div>
      </div>
      <!-- 热点信访事项分布 -->
      <div class="distribution">
        <div class="title">
          <img src="@/assets/images/homepage-distribution.png" alt="" />
          <span>热点信访事项分布</span>
        </div>
        <div class="chart">
          <distribution-chart />
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.container {
  background-image: url('@/assets/images/homepage-bg.png');
  background-size: 100% 182px;
  background-repeat: no-repeat;
  background-position: top;
  display: flex;
  flex-direction: column;

  .breadcrumb {
    width: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    column-gap: 9px;

    img {
      width: 11px;
      height: 14px;
    }

    span {
      font-size: 14px;
      color: #242f57;
    }
  }

  .statistics {
    width: 100%;
    flex-shrink: 0;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 10px;

    .statistic {
      flex: 1;
      background: #fff;
      box-shadow: 0 1px 6px 0 rgb(184 184 184 / 44%);
      border-radius: 4px;
      padding: 25px 60px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .name {
        display: flex;
        align-items: center;
        column-gap: 9px;

        img {
          width: 60px;
          height: 60px;
        }

        span {
          font-size: 16px;
          color: #61666f;
        }
      }

      .count {
        font-weight: bold;
        font-size: 40px;
        color: #242f57;
        line-height: 47px;
      }
    }
  }

  .layer {
    width: 100%;
    flex: 1;
    margin-top: 40px;
    display: grid;
    grid-template-columns: 930px 1fr 1fr;
    gap: 20px;

    .schedules {
      background: #fff;
      box-shadow: 0 1px 6px 0 rgb(184 184 184 / 44%);
      border-radius: 4px;
      padding: 20px;
      display: flex;
      flex-direction: column;

      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        column-gap: 12px;

        img {
          width: 17px;
          height: 18px;
        }

        span {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 30px;
        }
      }

      .schedule {
        width: 100%;
        height: fit-content;
        margin-top: 20px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: auto;
        grid-gap: 20px;

        .item {
          height: fit-content;
          padding: 20px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #c9d3de;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .left {
            display: flex;
            flex-direction: column;
            row-gap: 10px;

            > span:nth-of-type(1) {
              max-width: 250px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-weight: 500;
              font-size: 14px;
              color: #242f57;
              line-height: 20px;
            }

            > span:nth-of-type(2) {
              width: fit-content;
              font-size: 12px;
              color: #2a5caa;
              line-height: 17px;
              padding: 4px 7px;
              background: #eaf2ff;
              border-radius: 12px;
            }
          }

          .right {
            display: flex;
            flex-direction: column;
            row-gap: 10px;

            .endtime {
              display: flex;
              align-items: center;
              column-gap: 10px;

              span {
                font-size: 14px;
                color: #242f57;
                line-height: 20px;
              }
            }

            .t-button {
              background: #2a5caa;
              border-radius: 4px;
            }
          }
        }
      }
    }

    .cores {
      background: #fff;
      box-shadow: 0 1px 6px 0 rgb(184 184 184 / 44%);
      border-radius: 4px;
      padding: 20px;
      display: flex;
      flex-direction: column;

      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        column-gap: 12px;

        img {
          width: 17px;
          height: 18px;
        }

        span {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 30px;
        }
      }

      .core {
        width: 100%;
        flex: 1;
        margin-top: 20px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: 100px;
        gap: 20px;

        .item {
          height: fit-content;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding-top: 9px;
          row-gap: 10px;

          img {
            width: 42px;
            height: 42px;
          }

          div {
            width: 100%;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;

            span {
              font-size: 14px;
              color: #242f57;
              line-height: 20px;
              text-align: center;
            }
          }
        }
      }
    }

    .quicks {
      background: #fff;
      box-shadow: 0 1px 6px 0 rgb(184 184 184 / 44%);
      border-radius: 4px;
      padding: 20px;
      display: flex;
      flex-direction: column;

      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        column-gap: 12px;

        img {
          width: 17px;
          height: 18px;
        }

        span {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 30px;
        }
      }

      .quick {
        flex: 1;
        width: 100%;
        margin-top: 5cap;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: 100px;
        gap: 20px;

        .item {
          height: fit-content;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 21px 20px 25px;
          row-gap: 20px;
          background: #f1f4f9;
          border-radius: 4px;

          img {
            width: 73px;
            height: 74px;
          }

          span {
            font-size: 14px;
            color: #242f57;
            line-height: 20px;
          }
        }
      }
    }
  }

  .charts {
    width: 100%;
    flex: 1;
    margin-top: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    .trend {
      height: 100%;
      background: #fff;
      box-shadow: 0 1px 6px 0 rgb(184 184 184 / 44%);
      border-radius: 4px;
      padding: 20px;
      display: flex;
      flex-direction: column;

      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        column-gap: 12px;

        img {
          width: 17px;
          height: 18px;
        }

        span {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 30px;
        }
      }

      .chart {
        width: 100%;
        flex: 1;
        margin-top: 10px;
      }
    }

    .distribution {
      height: 100%;
      background: #fff;
      box-shadow: 0 1px 6px 0 rgb(184 184 184 / 44%);
      border-radius: 4px;
      padding: 20px;
      display: flex;
      flex-direction: column;

      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        column-gap: 12px;

        img {
          width: 17px;
          height: 18px;
        }

        span {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 30px;
        }
      }

      .chart {
        width: 100%;
        flex: 1;
        margin-top: 10px;
      }
    }
  }
}
</style>
