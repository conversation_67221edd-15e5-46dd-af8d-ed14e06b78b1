<template>
  <div ref="trendChartRef" style="width: 100%; height: 100%"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { defineProps, onMounted, onUnmounted, ref, watch } from 'vue';

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({
      dates: ['7-1', '7-2', '7-3', '7-4', '7-5', '7-6', '7-7'],
      totalVisits: [8, 15, 20, 25, 28, 30, 35],
      averageGrowth: [5, 10, 12, 15, 18, 20, 22],
      petitionLetters: [3, 6, 8, 10, 12, 15, 18],
      onlinePetitions: [4, 8, 10, 13, 15, 18, 20],
    }),
  },
});

const trendChartRef = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

// 初始化图表
const initChart = () => {
  if (trendChartRef.value) {
    chartInstance = echarts.init(trendChartRef.value);
    updateChart();

    // 响应窗口大小变化
    window.addEventListener('resize', handleResize);
  }
};

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return;

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: ['总体访问量', '平均增长', '信件处理', '网络信访'],
      top: 0, // 将图例位置改到顶部
      left: 'center', // 水平居中
      textStyle: {
        color: '#666', // 设置图例文字颜色
      },
    },
    grid: {
      left: '0%',
      right: '0%',
      bottom: '0%',
      top: '15%', // 适当增加顶部间距，为图例留出空间
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: props.chartData.dates,
      axisLine: {
        lineStyle: {
          color: '#E5E6EB',
        },
      },
      axisLabel: {
        color: '#61666F', // 设置x轴文字颜色
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    series: [
      {
        name: '总体访问量',
        type: 'bar',
        data: props.chartData.totalVisits,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#3FA0FF' },
            { offset: 1, color: '#2A5CAA' },
          ]),
          borderRadius: [48, 2, 48, 2],
        },
        barWidth: '20%',
      },
      {
        name: '平均增长',
        type: 'line',
        data: props.chartData.averageGrowth,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#52c41a',
        },
        lineStyle: {
          width: 2,
          type: 'dashed',
          color: '#52c41a',
        },
      },
      {
        name: '信件处理',
        type: 'line',
        data: props.chartData.petitionLetters,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#faad14',
        },
        lineStyle: {
          width: 2,
          type: 'dashed',
          color: '#faad14',
        },
      },
      {
        name: '网络信访',
        type: 'line',
        data: props.chartData.onlinePetitions,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#13c2c2',
        },
        lineStyle: {
          width: 2,
          type: 'dashed',
          color: '#13c2c2',
        },
      },
    ],
  };

  chartInstance.setOption(option);
};

// 处理窗口大小变化
const handleResize = () => {
  chartInstance?.resize();
};

// 监听数据变化
watch(
  () => props.chartData,
  () => {
    updateChart();
  },
  { deep: true },
);

// 组件挂载时初始化图表
onMounted(() => {
  initChart();
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  chartInstance?.dispose();
});
</script>

<style scoped>
.chart-mark {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgb(0 0 0 / 5%);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}
</style>
