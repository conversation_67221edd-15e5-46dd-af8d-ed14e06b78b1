<template>
  <div ref="chartContainer" style="width: 100%; height: 100%"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { onMounted, onUnmounted, ref } from 'vue';

// 直接在组件中定义数据
const chartData = {
  total: 1089,
  data: [
    { name: '拓扑补偿', value: 38, percentage: '26%' },
    { name: '信息完善', value: 24, percentage: '16%' },
    { name: '治安问题', value: 21, percentage: '14%' },
    { name: '物业管理', value: 17, percentage: '11%' },
    { name: '劳资纠纷', value: 15, percentage: '10%' },
    { name: '其他问题', value: 37, percentage: '10%' },
    { name: '环境污染', value: 28, percentage: '8%' },
    { name: '交通事故', value: 25, percentage: '7%' },
    { name: '噪音扰民', value: 22, percentage: '6%' },
    { name: '食品安全', value: 20, percentage: '5%' },
    { name: '消防隐患', value: 18, percentage: '4%' },
    { name: '医疗纠纷', value: 16, percentage: '3%' },
  ],
};

const chartContainer = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

const initChart = () => {
  if (chartContainer.value) {
    chartInstance = echarts.init(chartContainer.value);
    updateChart();
    window.addEventListener('resize', handleResize);
  }
};

const updateChart = () => {
  if (!chartInstance) return;

  const option = {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      type: 'plain',
      orient: 'vertical',
      right: '10%',
      top: '10%',
      itemWidth: 10,
      itemHeight: 10,
      icon: 'circle',
      formatter: (name: string) => {
        const item = chartData.data.find((d: { name: string }) => d.name === name);
        return [`{name|${name}}`, `{value|${item?.value}件}`, `{percent|${item?.percentage}}`].join('');
      },
      textStyle: {
        rich: {
          name: {
            color: '#242F57',
            fontSize: 14,
            padding: [0, 40, 0, 0], // 增加右侧内边距
          },
          value: {
            color: '#242F57',
            fontSize: 14,
            fontWeight: 500,
            padding: [0, 20, 0, 0], // 增加右侧内边距
          },
          percent: {
            color: '#61666F',
            fontSize: 14,
          },
        },
      },
      itemGap: 20,
      width: '45%',
      height: '80%',
      align: 'left',
      padding: [0, 0, 0, 30],
      grid: {
        left: 'center',
        top: 'top',
        width: 400, // 增加总宽度
        height: 'auto',
        columns: 2,
      },
    },
    series: [
      {
        type: 'pie',
        radius: ['65%', '85%'],
        center: ['20%', '50%'], // 将饼图中心点移到左侧
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: chartData.data.map((item: { name: string }) => ({
          ...item,
          itemStyle: {
            color: getColor(item.name),
          },
        })),
      },
      {
        type: 'pie',
        radius: ['0%', '50%'],
        center: ['20%', '50%'], // 将中心饼图也移到左侧
        label: {
          position: 'center',
          formatter: () => {
            return `{total|${chartData.total}}\n{title|总事项}`;
          },
          rich: {
            total: {
              fontSize: 24,
              fontWeight: 'bold',
              color: '#242F57',
              padding: [0, 0, 5, 0],
            },
            title: {
              fontSize: 14,
              color: '#61666F',
            },
          },
        },
        itemStyle: {
          color: '#fff',
        },
        data: [{ value: 1 }],
      },
    ],
  };

  chartInstance.setOption(option);
};

const getColor = (name: string) => {
  const colorMap = {
    拓扑补偿: '#2A5CAA',
    信息完善: '#FF9F40',
    治安问题: '#2B65EC',
    物业管理: '#52C41A',
    劳资纠纷: '#FF7875',
    其他问题: '#13C2C2',
    环境污染: '#975FE4',
    交通事故: '#F759AB',
    噪音扰民: '#FFA940',
    食品安全: '#36CFC9',
    消防隐患: '#FF7A45',
    医疗纠纷: '#597EF7',
  };
  return colorMap[name as keyof typeof colorMap] || '#1890FF';
};

const handleResize = () => {
  chartInstance?.resize();
};

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  chartInstance?.dispose();
});
</script>
