<script lang="ts">
export default {
  name: 'LetterProcessingCreate',
};
</script>

<script lang="ts" setup>
import { CloseIcon, SearchIcon, VerifyFilledIcon } from 'tdesign-icons-vue-next';
import { FormProps, MessagePlugin } from 'tdesign-vue-next';
import { reactive, ref } from 'vue';

import router from '@/router';
import { createLetterPetition } from '@/api/letter';
import { uploadMultipleFiles } from '@/api/upload';
import type { PetitionCreate } from '@/api/model/petitionModel';

const formData = reactive({
  // 信件基本信息
  letterNo: '',
  receivedDate: '',
  letterType: '',
  letterLevel: '',

  // 发信人信息
  petitionerName: '',
  petitionerGender: '',
  petitionerIdCard: '',
  petitionerPhone: '',
  petitionerAddress: '',

  // 信件内容
  title: '',
  category: '',
  content: '',
  attachments: [],

  // 其他信息
  priority: 0,
  isSensitive: false,
});

const formRules: FormProps['rules'] = {
  receivedDate: [{ required: true, message: '请选择收信日期' }],
  letterType: [{ required: true, message: '请选择信件类型' }],
  petitionerName: [{ required: true, message: '请输入发信人姓名' }],
  petitionerIdCard: [{ required: true, message: '请输入身份证号' }],
  petitionerPhone: [{ required: true, message: '请输入联系电话' }],
  petitionerAddress: [{ required: true, message: '请输入联系地址' }],
  title: [{ required: true, message: '请输入信件标题' }],
  category: [{ required: true, message: '请选择事项分类' }],
  content: [{ required: true, message: '请输入内容摘要' }],
};
const filesList = ref([
  {
    name: '这是一个上传成功的文件',
    status: 'success',
    url: 'https://tdesign.gtimg.com/site/source/figma-pc.png',
    size: 1000,
  },
  {
    name: '这是一个上传中的文件',
    status: 'progress',
    percent: 30,
    url: 'https://tdesign.gtimg.com/site/source/figma-pc.png',
    size: 1000,
  },
  {
    name: '这是一个上传失败的文件',
    status: 'fail',
    url: 'https://tdesign.gtimg.com/site/source/figma-pc.png',
    size: 1000,
  },
  {
    name: '这是一个等待上传的文件',
    status: 'waiting',
    url: 'https://tdesign.gtimg.com/site/source/figma-pc.png',
    size: 1000,
  },
]);

const loading = ref(false);
const uploadedFiles = ref([]);

// 文件上传处理
const handleFileUpload = async (files: File[]) => {
  if (files.length === 0) return;

  try {
    const response = await uploadMultipleFiles(files);
    uploadedFiles.value.push(...response.uploaded_files);
    formData.attachments = uploadedFiles.value;

    if (response.failed_files.length > 0) {
      MessagePlugin.warning(`${response.total_uploaded}个文件上传成功，${response.total_failed}个文件上传失败`);
    } else {
      MessagePlugin.success(`${response.total_uploaded}个文件上传成功`);
    }
  } catch (error) {
    console.error('文件上传失败:', error);
    MessagePlugin.error('文件上传失败');
  }
};

// 删除文件
const handleFileRemove = (index: number) => {
  uploadedFiles.value.splice(index, 1);
  formData.attachments = uploadedFiles.value;
};

const onReset: FormProps['onReset'] = () => {
  Object.keys(formData).forEach(key => {
    if (Array.isArray(formData[key])) {
      formData[key] = [];
    } else if (typeof formData[key] === 'boolean') {
      formData[key] = false;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

const onSubmit: FormProps['onSubmit'] = async ({ validateResult, firstError }) => {
  if (validateResult === true) {
    try {
      loading.value = true;

      // 构建API请求数据
      const petitionData: PetitionCreate = {
        type: 'LETTER',
        title: formData.title,
        content: formData.content,
        petitioner_name: formData.petitionerName,
        petitioner_phone: formData.petitionerPhone,
        petitioner_id_card: formData.petitionerIdCard,
        petitioner_address: formData.petitionerAddress,
        priority: formData.priority,
        is_sensitive: formData.isSensitive,
        letter_info: {
          received_date: formData.receivedDate,
          letter_source: formData.letterType,
          has_attachments: formData.attachments.length > 0,
          attachment_description: formData.attachments.length > 0 ? '已上传附件' : undefined,
        }
      };

      await createLetterPetition(petitionData);
      MessagePlugin.success('信件登记成功');
      router.push({ name: 'LetterProcessing' });
    } catch (error) {
      console.error('创建信件失败:', error);
      MessagePlugin.error('信件登记失败，请重试');
    } finally {
      loading.value = false;
    }
  } else {
    MessagePlugin.error(firstError || '请检查表单填写');
  }
};
</script>

<template>
  <div class="container">
    <div class="breadcrumb">
      <img src="@/assets/images/homepage-nav.png" alt="" />
      <span>信件处理 > 新建</span>
    </div>
    <div class="wrapper">
      <div class="title">
        <img src="@/assets/images/reception-create.png" alt="" />
        <span>新建信件登记</span>
      </div>

      <div class="steps">
        <div>
          <span>1</span>
          <span>信件收取</span>
        </div>
        <div>
          <span>2</span>
          <span>信件登记</span>
        </div>
        <div>
          <span>3</span>
          <span>信息审核</span>
        </div>
        <div>
          <span>4</span>
          <span>完成提交</span>
        </div>
      </div>

      <div class="form">
        <t-form ref="form" :rules="formRules" :data="formData" label-width="100px" @reset="onReset" @submit="onSubmit">
          <t-row :gutter="[20, 25]">
            <t-col :span="12">
              <div class="subtitle">信件基本信息</div>
            </t-col>
            <t-col :span="12">
              <t-row :gutter="20">
                <t-col :span="3">
                  <t-form-item name="letterNo" label="信件编号">
                    <t-input-adornment>
                      <t-input v-model="formData.letterNo" placeholder="系统自动生成" disabled></t-input>
                      <template #append>
                        <t-button theme="primary" content="生成" style="margin-left: 10px">
                          <template #icon>
                            <verify-filled-icon />
                          </template>
                        </t-button>
                      </template>
                    </t-input-adornment>
                  </t-form-item>
                </t-col>
                <t-col :span="3">
                  <t-form-item name="receivedDate" label="收信日期">
                    <t-date-picker v-model="formData.receivedDate" value-type="YYYY-MM-DD" clearable />
                  </t-form-item>
                </t-col>
                <t-col :span="3">
                  <t-form-item name="letterType" label="信件类型">
                    <t-select v-model="formData.letterType" placeholder="请选择" clearable>
                      <t-option value="邮寄" label="邮寄" />
                      <t-option value="传真" label="传真" />
                      <t-option value="电子邮件" label="电子邮件" />
                      <t-option value="其他" label="其他" />
                    </t-select>
                  </t-form-item>
                </t-col>
                <t-col :span="3">
                  <t-form-item name="letterLevel" label="信件密级">
                    <t-select v-model="formData.letterLevel" placeholder="请选择" clearable>
                      <t-option value="普通" label="普通" />
                      <t-option value="重要" label="重要" />
                      <t-option value="紧急" label="紧急" />
                      <t-option value="特急" label="特急" />
                    </t-select>
                  </t-form-item>
                </t-col>
              </t-row>
            </t-col>
            <t-col :span="12"><div class="line"></div></t-col>
            <t-col :span="12">
              <div class="subtitle">发信人信息</div>
            </t-col>
            <t-col :span="3">
              <t-form-item label="查找信访人">
                <t-input-adornment>
                  <t-input placeholder="请输入姓名/身份证号查找"></t-input>
                  <template #append>
                    <t-button theme="primary">
                      <template #icon>
                        <search-icon />
                      </template>
                    </t-button>
                  </template>
                </t-input-adornment>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label-width="0">
                <div class="tip">
                  <span>提示：系统检测到当前信访人存在历史信访记录，可能为重复信访情况。</span>
                  <t-button theme="primary" size="small" content="查看记录" />
                </div>
              </t-form-item>
            </t-col>

            <t-col :span="3">
              <t-form-item name="petitionerName" label="姓名">
                <t-input v-model="formData.petitionerName" placeholder="请输入"></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="3">
              <t-form-item name="petitionerGender" label="性别">
                <t-select v-model="formData.petitionerGender" placeholder="请选择" clearable>
                  <t-option value="男" label="男" />
                  <t-option value="女" label="女" />
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="3">
              <t-form-item name="petitionerIdCard" label="身份证号">
                <t-input v-model="formData.petitionerIdCard" placeholder="请输入"></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="3">
              <t-form-item name="petitionerPhone" label="联系电话">
                <t-input v-model="formData.petitionerPhone" placeholder="请输入"></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item name="petitionerAddress" label="联系地址">
                <t-input v-model="formData.petitionerAddress" placeholder="请输入"></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="12"><div class="line"></div></t-col>
            <t-col :span="12">
              <div class="subtitle">信件内容</div>
            </t-col>
            <t-col :span="6">
              <t-form-item name="title" label="信件标题">
                <t-input v-model="formData.title" placeholder="请输入"></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="3">
              <t-form-item name="category" label="事项分类">
                <t-select v-model="formData.category" placeholder="请选择" clearable>
                  <t-option value="劳动纠纷" label="劳动纠纷" />
                  <t-option value="房屋拆迁" label="房屋拆迁" />
                  <t-option value="医疗纠纷" label="医疗纠纷" />
                  <t-option value="教育问题" label="教育问题" />
                  <t-option value="环境保护" label="环境保护" />
                  <t-option value="社会保障" label="社会保障" />
                  <t-option value="土地纠纷" label="土地纠纷" />
                  <t-option value="交通问题" label="交通问题" />
                  <t-option value="消费维权" label="消费维权" />
                  <t-option value="其他" label="其他" />
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="3">
              <t-form-item label="紧急程度">
                <t-select v-model="formData.priority" placeholder="请选择" clearable>
                  <t-option :value="0" label="普通" />
                  <t-option :value="1" label="重要" />
                  <t-option :value="2" label="紧急" />
                  <t-option :value="3" label="特急" />
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item name="content" label="内容摘要">
                <t-textarea
                  v-model="formData.content"
                  placeholder="请输入"
                  :autosize="{ minRows: 3, maxRows: 5 }"
                ></t-textarea>
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="敏感标记">
                <t-checkbox v-model="formData.isSensitive">标记为敏感信访</t-checkbox>
              </t-form-item>
            </t-col>

            <t-col :span="12">
              <t-form-item label="上传附件">
                <t-upload
                  multiple
                  :auto-upload="false"
                  :size-limit="{ size: 10, unit: 'MB' }"
                  :max="5"
                  :allow-upload-duplicate-file="false"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.txt,.zip,.rar"
                  @change="handleFileUpload"
                >
                  <t-button theme="primary">选择文件</t-button>
                </t-upload>

                <!-- 已上传文件列表 -->
                <div v-if="uploadedFiles.length > 0" class="uploaded-files">
                  <div v-for="(file, index) in uploadedFiles" :key="index" class="file-item">
                    <span>{{ file.filename }}</span>
                    <span class="file-size">{{ (file.file_size / 1024).toFixed(1) }}KB</span>
                    <t-button
                      theme="danger"
                      variant="text"
                      size="small"
                      @click="handleFileRemove(index)"
                    >
                      删除
                    </t-button>
                  </div>
                </div>
              </t-form-item>
            </t-col>
          </t-row>
        </t-form>
      </div>
      <div class="submit">
        <t-button content="取 消" theme="default" @click="router.back()" />
        <t-button content="重 置" theme="default" @click="onReset" />
        <t-button content="提 交" theme="primary" :loading="loading" @click="onSubmit" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 10px;

  .breadcrumb {
    width: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    column-gap: 9px;

    img {
      width: 11px;
      height: 14px;
    }

    span {
      font-size: 14px;
      color: #242f57;
    }
  }

  .wrapper {
    width: 100%;
    flex: 1;
    background: #fff;
    box-shadow: 0 2px 4px 0 rgb(28 41 90 / 4%);
    border-radius: 4px;
    border: 1px solid #eaedf7;
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: #f0f4f9;
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background-color: rgba(#2a5caa, 0.5);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-button {
      display: none;
      width: 0;
      height: 0;
    }

    .title {
      width: 100%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      column-gap: 10px;

      span {
        font-weight: 500;
        font-size: 20px;
        color: #242f57;
        line-height: 30px;
      }
    }

    .steps {
      width: 100%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 16px;

      > div {
        width: 182px;
        height: 42px;
        display: flex;
        align-items: center;
        padding-left: 35px;
        column-gap: 15px;
        background: url('@/assets/images/reception-step.png') no-repeat center / 100%;
        font-weight: 500;
        font-size: 16px;
        color: #fff;
        line-height: 22px;

        > span:nth-of-type(1) {
          width: 22px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-radius: 50%;
          border: 1px solid #fff;
        }
      }
    }

    .form {
      width: 100%;
      flex: 1;
      padding: 0 40px;
      overflow-x: hidden;
      overflow-y: auto;
      margin-top: 20px;

      :deep(.t-form) {
        .t-form__label {
          font-size: 14px;
          color: #353637;
          padding-right: 10px;
        }

        .warning {
          color: #dd1f1f;
          font-size: 14px;
          font-weight: 500;
        }

        .line {
          height: 1px;
          background: #c9d3de;
        }
      }

      .subtitle {
        font-weight: 500;
        font-size: 16px;
        color: #242f57;
        line-height: 22px;
      }

      .tip {
        background: #fceede;
        padding: 5px 5px 5px 15px;

        span {
          font-size: 14px;
          color: #242f57;
          line-height: 20px;
        }
      }
    }

    .submit {
      width: 100%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
    }
  }
}

.uploaded-files {
  margin-top: 12px;

  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 8px;

    .file-size {
      color: #666;
      font-size: 12px;
    }
  }
}
</style>
