<template>
  <div class="container">
    <div class="breadcrumb">
      <img src="@/assets/images/homepage-nav.png" alt="" />
      <span>信件处理 > 详情</span>
    </div>
    <div class="wrapper">
      <div class="wrapper-left">
        <div class="petition-info">
          <div class="title">
            <img src="@/assets/images/homepage-schedule.png" alt="" />
            <span>信件信息</span>
          </div>
          <div class="info">
            <div class="item">
              <div class="label">信件编号</div>
              <div class="value">{{ petitionInfo.petition_no || 'XF20250520001' }}</div>
            </div>
            <div class="item">
              <div class="label">标题</div>
              <div class="value">{{ petitionInfo.title || '关于城市道路设施改善的建议' }}</div>
            </div>
            <div class="item">
              <div class="label">接收日期</div>
              <div class="value">{{ formatDate(letterInfo.received_date) }}</div>
            </div>
            <div class="item">
              <div class="label">信件来源</div>
              <div class="value">{{ letterInfo.letter_source || '邮局' }}</div>
            </div>
            <div class="item">
              <div class="label">当前状态</div>
              <div class="value">
                <t-tag theme="primary" variant="light">{{ translateStatus(petitionInfo.status) }}</t-tag>
              </div>
            </div>
            <div class="item">
              <div class="label">紧急程度</div>
              <div class="value">
                <t-tag theme="warning" variant="light" v-if="petitionInfo.priority > 1">紧急</t-tag>
                <t-tag theme="success" variant="light" v-else>普通</t-tag>
              </div>
            </div>
            <div class="item">
              <div class="label">处理人</div>
              <div class="value">{{ petitionInfo.handler_name || '未分配' }}</div>
            </div>
            <div class="item">
              <div class="label">是否有附件</div>
              <div class="value">{{ letterInfo.has_attachments ? '是' : '否' }}</div>
            </div>
          </div>

          <div class="title">
            <img src="@/assets/images/homepage-schedule.png" alt="" />
            <span>信访人信息</span>
          </div>
          <div class="info">
            <div class="item">
              <div class="label">姓名</div>
              <div class="value">{{ petitionInfo.petitioner_name || '张三' }}</div>
            </div>
            <div class="item">
              <div class="label">联系电话</div>
              <div class="value">{{ petitionInfo.petitioner_phone || '13800138000' }}</div>
            </div>
            <div class="item">
              <div class="label">身份证号</div>
              <div class="value">{{ petitionInfo.petitioner_id_card || '110101********0123' }}</div>
            </div>
            <div class="item">
              <div class="label">家庭住址</div>
              <div class="value">{{ petitionInfo.petitioner_address || '北京市朝阳区建国路15号' }}</div>
            </div>
          </div>

          <div class="title">
            <img src="@/assets/images/homepage-schedule.png" alt="" />
            <span>信访内容</span>
          </div>
          <div class="content">
            {{ petitionInfo.content || '关于我市部分城区道路设施老化的问题，提出以下几点建议：1、加强道路基础设施巡检； 2、增加人行道无障碍设施；3、优化交通信号灯配时...调整合理的通行时间，减缓交通拥堵情况，提升市民出行体验。此外，建议对老旧小区周边道路进行综合整治，解决路面坑洼、井盖损坏等常见问题。' }}
          </div>

          <div v-if="letterInfo.has_attachments" class="title">
            <img src="@/assets/images/homepage-schedule.png" alt="" />
            <span>附件信息</span>
          </div>
          <div v-if="letterInfo.has_attachments" class="attachments">
            <div class="attachment-item">
              <t-link href="#" theme="primary">{{ letterInfo.attachment_description || '附件.pdf' }}</t-link>
            </div>
          </div>

          <div class="title">
            <img src="@/assets/images/homepage-schedule.png" alt="" />
            <span>处理过程</span>
          </div>
          <div class="process">
            <t-steps :current="processingRecords.length" layout="vertical">
              <t-step-item
                v-for="(record, index) in processingRecords"
                :key="index"
                :title="formatDate(record.created_at)"
                :content="record.action + (record.note ? ': ' + record.note : '')"
              />
            </t-steps>
          </div>
        </div>
      </div>

      <div class="wrapper-right">
        <div class="operations">
          <div class="title">
            <img src="@/assets/images/homepage-core.png" alt="" />
            <span>操作</span>
          </div>
          <div class="buttons">
            <t-button theme="primary" @click="showAssignModal = true">分配处理</t-button>
            <t-button theme="primary" @click="showStatusModal = true">更新状态</t-button>
            <t-button theme="warning" @click="showTransferModal = true">转 办</t-button>
            <t-button theme="default">打印信件</t-button>
          </div>

          <div class="title">
            <img src="@/assets/images/homepage-core.png" alt="" />
            <span>AI助手</span>
          </div>
          <div class="assistant">
            <div class="message ai-message">
              <div class="message-title">智能分析结果</div>
              <div class="message-content">
                <p>基于对信件内容的分析，此信访类型为"基础设施建设"，内容涉及道路设施改善。</p>
                <p>检测到与案件编号 XF20240210005 有相似内容，可能为重复信访。</p>
                <p>建议优先级：中等</p>
                <p>建议处理部门：市政工程管理处</p>
              </div>
            </div>
            <div class="message ai-message">
              <div class="message-title">回复建议</div>
              <div class="message-content">
                <p>您好，感谢您对我市城市道路设施提出的宝贵建议。我局已安排工作人员对您提及的问题进行调查，并将根据实际情况制定改善计划。</p>
                <p>关于老旧小区周边道路整治问题，我们已纳入今年的市政工程计划中，预计在下个季度实施。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分配处理人员模态框 -->
    <t-dialog
      v-model:visible="showAssignModal"
      header="分配处理人员"
      :on-confirm="handleAssign"
      :on-close="() => (showAssignModal = false)"
    >
      <template #body>
        <div class="dialog-content">
          <t-select v-model="selectedHandlerId" placeholder="请选择处理人员">
            <t-option v-for="user in users" :key="user.id" :value="user.id" :label="user.username" />
          </t-select>
        </div>
      </template>
    </t-dialog>

    <!-- 更新状态模态框 -->
    <t-dialog
      v-model:visible="showStatusModal"
      header="更新处理状态"
      :on-confirm="handleStatusUpdate"
      :on-close="() => (showStatusModal = false)"
    >
      <template #body>
        <div class="dialog-content">
          <t-select v-model="selectedStatus" placeholder="请选择状态">
            <t-option value="PENDING" label="待处理" />
            <t-option value="PROCESSING" label="处理中" />
            <t-option value="COMPLETED" label="已处理" />
            <t-option value="CLOSED" label="已关闭" />
          </t-select>
          <t-textarea v-model="statusNote" placeholder="请输入处理意见（选填）" :autosize="{ minRows: 3, maxRows: 5 }" style="margin-top: 16px" />
        </div>
      </template>
    </t-dialog>

    <!-- 转办模态框 -->
    <t-dialog
      v-model:visible="showTransferModal"
      header="转办信访"
      :on-confirm="handleTransfer"
      :on-close="() => (showTransferModal = false)"
    >
      <template #body>
        <div class="dialog-content">
          <t-select v-model="selectedDepartment" placeholder="请选择部门">
            <t-option value="1" label="市政工程管理处" />
            <t-option value="2" label="城市规划局" />
            <t-option value="3" label="交通管理局" />
          </t-select>
          <t-textarea v-model="transferNote" placeholder="请输入转办说明" :autosize="{ minRows: 3, maxRows: 5 }" style="margin-top: 16px" />
        </div>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { getLetterPetitionDetail, assignLetterPetitionHandler, updateLetterPetitionStatus } from '@/api/letter';
import { getUsers } from '@/api/auth';
import type { Petition, LetterInfo } from '@/api/model/petitionModel';

const route = useRoute();
const router = useRouter();
const petitionId = ref<string | number>(route.params.id as string);
const loading = ref(false);

// 信访详情数据
const petitionInfo = ref<Petition>({
  id: 0,
  petition_no: '',
  title: '',
  content: '',
  type: 'LETTER',
  petitioner_name: '',
  petitioner_phone: '',
  petitioner_id_card: '',
  petitioner_address: '',
  status: 'PENDING',
  priority: 0,
  is_sensitive: false,
  created_at: '',
  updated_at: '',
  handler_id: undefined,
});

// 信件特定信息
const letterInfo = ref<LetterInfo>({
  received_date: '',
  letter_source: '',
  has_attachments: false,
  attachment_description: ''
});

// 处理记录
const processingRecords = ref([
  {
    id: 1,
    action: '创建信访',
    note: '',
    created_at: '2025-05-20T10:00:00'
  }
]);

// 用户列表
const users = ref([]);

// 获取用户列表
const fetchUsers = async () => {
  try {
    const response = await getUsers();
    users.value = response.items;
  } catch (error) {
    console.error('获取用户列表失败:', error);
    MessagePlugin.error('获取用户列表失败');
  }
};

// 模态框控制
const showAssignModal = ref(false);
const showStatusModal = ref(false);
const showTransferModal = ref(false);

// 表单数据
const selectedHandlerId = ref(null);
const selectedStatus = ref('');
const statusNote = ref('');
const selectedDepartment = ref('');
const transferNote = ref('');

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 状态翻译
const translateStatus = (status) => {
  const statusMap = {
    PENDING: '待处理',
    PROCESSING: '处理中',
    COMPLETED: '已处理',
    CLOSED: '已关闭'
  };
  return statusMap[status] || status;
};

// 分配处理人
const handleAssign = async () => {
  if (!selectedHandlerId.value) {
    MessagePlugin.warning('请选择处理人');
    return;
  }

  try {
    await assignLetterPetitionHandler(petitionId.value, selectedHandlerId.value);
    MessagePlugin.success('分配处理人成功');
    showAssignModal.value = false;
    selectedHandlerId.value = null;

    // 重新获取详情
    fetchPetitionDetail();
  } catch (error) {
    MessagePlugin.error('分配处理人失败');
    console.error('分配处理人失败:', error);
  }
};

// 更新状态
const handleStatusUpdate = async () => {
  if (!selectedStatus.value) {
    MessagePlugin.warning('请选择状态');
    return;
  }

  try {
    await updateLetterPetitionStatus(petitionId.value, {
      status: selectedStatus.value,
      note: statusNote.value
    });
    MessagePlugin.success('更新状态成功');
    showStatusModal.value = false;

    // 重置表单
    selectedStatus.value = '';
    statusNote.value = '';

    // 重新获取详情
    fetchPetitionDetail();
  } catch (error) {
    MessagePlugin.error('更新状态失败');
    console.error('更新状态失败:', error);
  }
};

// 转办
const handleTransfer = async () => {
  try {
    // 这里应该调用转办API
    MessagePlugin.success('转办成功');
    showTransferModal.value = false;

    // 更新处理记录
    processingRecords.value.push({
      id: processingRecords.value.length + 1,
      action: '转办',
      note: `转办至部门ID${selectedDepartment.value}: ${transferNote.value}`,
      created_at: new Date().toISOString()
    });

    // 重置表单
    transferNote.value = '';
  } catch (error) {
    MessagePlugin.error('转办失败');
  }
};

// 获取信访详情
const fetchPetitionDetail = async () => {
  if (!petitionId.value) {
    MessagePlugin.error('信访ID不存在');
    return;
  }

  try {
    loading.value = true;
    const data = await getLetterPetitionDetail(petitionId.value);
    petitionInfo.value = data;
    letterInfo.value = data.letter_info || {
      received_date: '',
      letter_source: '',
      has_attachments: false,
      attachment_description: ''
    };
    processingRecords.value = data.processing_records || [];
    loading.value = false;
  } catch (error) {
    loading.value = false;
    MessagePlugin.error('获取信访详情失败');
    console.error('获取信访详情失败:', error);
  }
};

onMounted(() => {
  // 获取详情数据
  fetchPetitionDetail();
  // 获取用户列表
  fetchUsers();
});
</script>

<style scoped lang="less">
.container {
  padding: 20px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
}

.wrapper {
  display: flex;
  gap: 20px;
}

.wrapper-left {
  flex: 3;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
}

.wrapper-right {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  min-width: 300px;
}

.title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  margin-top: 24px;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 8px;

  &:first-child {
    margin-top: 0;
  }

  img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }

  span {
    font-size: 16px;
    font-weight: bold;
  }
}

.info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;

  .item {
    display: flex;
    align-items: center;

    .label {
      min-width: 80px;
      color: #666;
    }

    .value {
      flex: 1;
    }
  }
}

.content {
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 16px;
  white-space: pre-line;
}

.attachments {
  margin-bottom: 16px;
}

.operations {
  .buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
  }
}

.assistant {
  .message {
    margin-bottom: 16px;
    padding: 12px;
    border-radius: 4px;

    &.ai-message {
      background-color: #f0f9ff;
    }

    .message-title {
      font-weight: bold;
      margin-bottom: 8px;
    }

    .message-content {
      p {
        margin: 0;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.dialog-content {
  padding: 8px 0;
}
</style>
