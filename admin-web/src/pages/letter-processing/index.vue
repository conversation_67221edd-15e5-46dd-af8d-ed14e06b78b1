<script lang="ts" setup>
import { AddCircleIcon, ChevronRightSIcon } from 'tdesign-icons-vue-next';
import { TableProps } from 'tdesign-vue-next';
import { ref } from 'vue';

import useTableHeight from '@/composables/useTableHeight';
import router from '@/router';

const { tableHeight } = useTableHeight();

const tableData = ref<TableProps['data']>([
  {
    index: 1,
    receptionNo: 'RC-20230001',
    visitor: '张三',
    visitTime: '2023-01-15 09:30',
    appealType: '劳动纠纷',
    status: '已处理',
  },
  {
    index: 2,
    receptionNo: 'RC-20230002',
    visitor: '李四',
    visitTime: '2023-01-16 10:15',
    appealType: '房屋拆迁',
    status: '处理中',
  },
  {
    index: 3,
    receptionNo: 'RC-20230003',
    visitor: '王五',
    visitTime: '2023-01-17 14:20',
    appealType: '医疗纠纷',
    status: '已处理',
  },
  {
    index: 4,
    receptionNo: 'RC-20230004',
    visitor: '赵六',
    visitTime: '2023-01-18 11:00',
    appealType: '教育问题',
    status: '待处理',
  },
  {
    index: 5,
    receptionNo: 'RC-20230005',
    visitor: '钱七',
    visitTime: '2023-01-19 15:30',
    appealType: '环境保护',
    status: '已处理',
  },
  {
    index: 6,
    receptionNo: 'RC-20230006',
    visitor: '孙八',
    visitTime: '2023-01-20 10:45',
    appealType: '社会保障',
    status: '处理中',
  },
  {
    index: 7,
    receptionNo: 'RC-20230007',
    visitor: '周九',
    visitTime: '2023-01-21 13:10',
    appealType: '土地纠纷',
    status: '已处理',
  },
  {
    index: 8,
    receptionNo: 'RC-20230008',
    visitor: '吴十',
    visitTime: '2023-01-22 09:20',
    appealType: '交通问题',
    status: '待处理',
  },
  {
    index: 9,
    receptionNo: 'RC-20230009',
    visitor: '郑十一',
    visitTime: '2023-01-23 14:50',
    appealType: '消费维权',
    status: '已处理',
  },
  {
    index: 10,
    receptionNo: 'RC-20230010',
    visitor: '王十二',
    visitTime: '2023-01-24 11:30',
    appealType: '劳动纠纷',
    status: '处理中',
  },
  {
    index: 11,
    receptionNo: 'RC-20230011',
    visitor: '李十三',
    visitTime: '2023-01-25 10:00',
    appealType: '房屋拆迁',
    status: '已处理',
  },
  {
    index: 12,
    receptionNo: 'RC-20230012',
    visitor: '张十四',
    visitTime: '2023-01-26 15:15',
    appealType: '医疗纠纷',
    status: '待处理',
  },
  {
    index: 13,
    receptionNo: 'RC-20230013',
    visitor: '刘十五',
    visitTime: '2023-01-27 09:40',
    appealType: '教育问题',
    status: '已处理',
  },
  {
    index: 14,
    receptionNo: 'RC-20230014',
    visitor: '陈十六',
    visitTime: '2023-01-28 14:00',
    appealType: '环境保护',
    status: '处理中',
  },
  {
    index: 15,
    receptionNo: 'RC-20230015',
    visitor: '杨十七',
    visitTime: '2023-01-29 11:20',
    appealType: '社会保障',
    status: '已处理',
  },
  {
    index: 16,
    receptionNo: 'RC-20230016',
    visitor: '黄十八',
    visitTime: '2023-01-30 10:30',
    appealType: '土地纠纷',
    status: '待处理',
  },
  {
    index: 17,
    receptionNo: 'RC-20230017',
    visitor: '赵十九',
    visitTime: '2023-01-31 13:45',
    appealType: '交通问题',
    status: '已处理',
  },
  {
    index: 18,
    receptionNo: 'RC-20230018',
    visitor: '周二十',
    visitTime: '2023-02-01 09:10',
    appealType: '消费维权',
    status: '处理中',
  },
  {
    index: 19,
    receptionNo: 'RC-20230019',
    visitor: '吴二十一',
    visitTime: '2023-02-02 14:30',
    appealType: '劳动纠纷',
    status: '已处理',
  },
  {
    index: 20,
    receptionNo: 'RC-20230020',
    visitor: '郑二十二',
    visitTime: '2023-02-03 11:50',
    appealType: '房屋拆迁',
    status: '待处理',
  },
  {
    index: 21,
    receptionNo: 'RC-20230021',
    visitor: '林二十三',
    visitTime: '2023-02-04 09:15',
    appealType: '医疗纠纷',
    status: '已处理',
  },
  {
    index: 22,
    receptionNo: 'RC-20230022',
    visitor: '王二十四',
    visitTime: '2023-02-05 14:30',
    appealType: '教育问题',
    status: '处理中',
  },
  {
    index: 23,
    receptionNo: 'RC-20230023',
    visitor: '张二十五',
    visitTime: '2023-02-06 10:45',
    appealType: '环境保护',
    status: '待处理',
  },
  {
    index: 24,
    receptionNo: 'RC-20230024',
    visitor: '李二十六',
    visitTime: '2023-02-07 13:20',
    appealType: '社会保障',
    status: '已处理',
  },
  {
    index: 25,
    receptionNo: 'RC-20230025',
    visitor: '刘二十七',
    visitTime: '2023-02-08 11:10',
    appealType: '土地纠纷',
    status: '处理中',
  },
  {
    index: 26,
    receptionNo: 'RC-20230026',
    visitor: '陈二十八',
    visitTime: '2023-02-09 15:30',
    appealType: '交通问题',
    status: '已处理',
  },
  {
    index: 27,
    receptionNo: 'RC-20230027',
    visitor: '杨二十九',
    visitTime: '2023-02-10 09:40',
    appealType: '消费维权',
    status: '待处理',
  },
  {
    index: 28,
    receptionNo: 'RC-20230028',
    visitor: '黄三十',
    visitTime: '2023-02-11 14:15',
    appealType: '劳动纠纷',
    status: '已处理',
  },
  {
    index: 29,
    receptionNo: 'RC-20230029',
    visitor: '赵三十一',
    visitTime: '2023-02-12 10:20',
    appealType: '房屋拆迁',
    status: '处理中',
  },
  {
    index: 30,
    receptionNo: 'RC-20230030',
    visitor: '周三十二',
    visitTime: '2023-02-13 13:45',
    appealType: '医疗纠纷',
    status: '已处理',
  },
  {
    index: 31,
    receptionNo: 'RC-20230031',
    visitor: '吴三十三',
    visitTime: '2023-02-14 09:30',
    appealType: '教育问题',
    status: '待处理',
  },
  {
    index: 32,
    receptionNo: 'RC-20230032',
    visitor: '郑三十四',
    visitTime: '2023-02-15 14:50',
    appealType: '环境保护',
    status: '已处理',
  },
  {
    index: 33,
    receptionNo: 'RC-20230033',
    visitor: '王三十五',
    visitTime: '2023-02-16 11:25',
    appealType: '社会保障',
    status: '处理中',
  },
  {
    index: 34,
    receptionNo: 'RC-20230034',
    visitor: '李三十六',
    visitTime: '2023-02-17 10:15',
    appealType: '土地纠纷',
    status: '已处理',
  },
  {
    index: 35,
    receptionNo: 'RC-20230035',
    visitor: '张三十七',
    visitTime: '2023-02-18 13:30',
    appealType: '交通问题',
    status: '待处理',
  },
  {
    index: 36,
    receptionNo: 'RC-20230036',
    visitor: '刘三十八',
    visitTime: '2023-02-19 09:50',
    appealType: '消费维权',
    status: '已处理',
  },
  {
    index: 37,
    receptionNo: 'RC-20230037',
    visitor: '陈三十九',
    visitTime: '2023-02-20 14:10',
    appealType: '劳动纠纷',
    status: '处理中',
  },
  {
    index: 38,
    receptionNo: 'RC-20230038',
    visitor: '杨四十',
    visitTime: '2023-02-21 11:40',
    appealType: '房屋拆迁',
    status: '已处理',
  },
  {
    index: 39,
    receptionNo: 'RC-20230039',
    visitor: '黄四十一',
    visitTime: '2023-02-22 10:05',
    appealType: '医疗纠纷',
    status: '待处理',
  },
  {
    index: 40,
    receptionNo: 'RC-20230040',
    visitor: '赵四十二',
    visitTime: '2023-02-23 13:15',
    appealType: '教育问题',
    status: '已处理',
  },
]);
const tableColumns = ref<TableProps['columns']>([
  {
    colKey: 'serial-number',
    title: '序号',
    align: 'center',
  },
  {
    colKey: 'receptionNo',
    title: '信件编号',
    align: 'center',
  },
  {
    colKey: 'visitor',
    title: '发信人',
    align: 'center',
  },
  {
    colKey: 'visitTime',
    title: '接收时间',
    align: 'center',
  },
  {
    colKey: 'appealType',
    title: '信件类型',
    align: 'center',
  },
  {
    colKey: 'urgency',
    title: '紧急程度',
    align: 'center',
  },
  {
    colKey: 'status',
    title: '处理状态',
    align: 'center',
  },
  {
    colKey: 'duplicate',
    title: '重复情况',
    align: 'center',
  },
  {
    colKey: 'operation',
    title: '操作',
    align: 'center',
    cell: (h, { row }) => {
      return h(
        't-button',
        {
          variant: 'text',
          onClick: () => handleDetail(row),
        },
        '详情',
      );
    },
  },
]);

const handleDetail = (row: any) => {
  // 处理详情操作逻辑
  console.log('查看详情:', row);
};
</script>

<template>
  <div class="container">
    <div class="breadcrumb">
      <img src="@/assets/images/homepage-nav.png" alt="" />
      <span>信件处理</span>
    </div>
    <div class="wrapper">
      <div class="table">
        <div class="title">
          <img src="@/assets/images/homepage-schedule.png" alt="" />
          <span>信件管理</span>
        </div>
        <div class="search">
          <div class="input">
            <t-input-adornment>
              <template #append>
                <img style="width: 20px; height: 20px" src="@/assets/images/search.png" alt="" />
              </template>
              <t-tag-input placeholder="请输入内容" />
            </t-input-adornment>
          </div>
          <t-button @click="router.push({ name: 'LetterProcessingCreate' })">
            <template #icon><add-circle-icon /></template>
            新增信件登录
          </t-button>
        </div>
        <div class="t-table-wrapper">
          <t-table
            row-key="index"
            bordered
            :max-height="tableHeight"
            :data="tableData"
            :columns="tableColumns"
          ></t-table>
        </div>
      </div>
      <div class="receptions">
        <div class="title">
          <img src="@/assets/images/homepage-core.png" alt="" />
          <span>信件处理功能区</span>
        </div>
        <div class="reception">
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/letter-icon1.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">智能分拣与分类</div>
              <div class="subtitle">利用自然语言处理技术，对信件内容进行主题识别、情感分析，并自动分类。</div>
            </div>
          </div>
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/letter-icon2.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">案件判重（比对分析）</div>
              <div class="subtitle">通过AI判重功能，识别重复投诉，加快信件处理效率。</div>
            </div>
          </div>
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/letter-icon3.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">流程标准化与时效监控</div>
              <div class="subtitle">自动跟踪各环节处理时效，超期自动催办，提高信访件处理效率。</div>
            </div>
          </div>
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/letter-icon4.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">OCR文字识别</div>
              <div class="subtitle">对信访材料（图片、PDF）进行OCR识别，提取文字内容，辅助登记处理。</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 10px;

  .breadcrumb {
    width: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    column-gap: 9px;

    img {
      width: 11px;
      height: 14px;
    }

    span {
      font-size: 14px;
      color: #242f57;
    }
  }

  .wrapper {
    width: 100%;
    flex: 1;
    column-gap: 20px;
    display: flex;

    .table {
      flex: 1;
      padding: 20px;
      background: #fff;
      box-shadow: 0 2px 4px 0 rgb(28 41 90 / 4%);
      border-radius: 4px;
      border: 1px solid #eaedf7;
      display: flex;
      flex-direction: column;
      row-gap: 20px;

      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        column-gap: 12px;

        img {
          width: 17px;
          height: 18px;
        }

        span {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 30px;
        }
      }

      .search {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .input {
          flex: 1;
          display: flex;
          justify-content: center;

          :deep(.t-input-adornment) {
            width: 718px;
            border-radius: 21px;
            border: 1px solid #dddfe5;
            background: #f6f8f9;

            .t-input__wrap {
              .t-input {
                height: 42px;
                padding: 0 20px;
                background: #f6f8f9;
                border-radius: 21px 0 0 21px;
                border: none;
              }

              .t-is-focused {
                box-shadow: none;
              }
            }

            .t-input-adornment__append {
              width: 70px;
              height: 42px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #2a5caa;
              border-radius: 21px;
              margin-left: 0;
              cursor: pointer;
            }
          }
        }

        .t-button {
          padding: 0 35px;
          height: 42px;
          background: #2a5caa;
          border-radius: 4px;
        }
      }

      .t-table-wrapper {
        width: 100%;
        flex: 1;
      }
    }

    .receptions {
      flex-shrink: 0;
      height: fit-content;
      background: #fff;
      box-shadow: 0 2px 4px 0 rgb(28 41 90 / 4%);
      border-radius: 4px;
      padding: 20px;

      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        column-gap: 12px;

        img {
          width: 17px;
          height: 18px;
        }

        span {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 30px;
        }
      }

      .reception {
        width: 100%;
        flex: 1;
        margin-top: 20px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 20px;

        .item {
          background: #f0f4f9;
          border-radius: 4px;
          padding: 16px 20px 10px;
          cursor: pointer;

          .jump {
            display: flex;
            align-items: center;
            justify-content: space-between;
            column-gap: 10px;
            margin-bottom: 10px;

            img {
              width: 42px;
              height: 42px;
            }

            .t-icon {
              font-size: 24px;
              color: #2a5caa;
            }
          }

          .introduce {
            width: 153px;
            word-break: break-all;

            .title {
              font-weight: 500;
              font-size: 14px;
              color: #242f57;
              line-height: 20px;
            }

            .subtitle {
              font-weight: 400;
              font-size: 12px;
              color: #61666f;
              line-height: 17px;
              text-align: justify;
              margin-top: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
