<script lang="ts" setup>
import { AddCircleIcon, ChevronRightSIcon } from 'tdesign-icons-vue-next';
import { TableProps, MessagePlugin } from 'tdesign-vue-next';
import { ref, onMounted, reactive } from 'vue';

import useTableHeight from '@/composables/useTableHeight';
import router from '@/router';
import { getLetterPetitions } from '@/api/letter';
import type { Petition } from '@/api/model/petitionModel';

const { tableHeight } = useTableHeight();

// 响应式数据
const tableData = ref<Petition[]>([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
});

// 搜索参数
const searchParams = reactive({
  query: '',
  status: '',
  start_date: '',
  end_date: '',
});

// 获取信件列表数据
const fetchLetterPetitions = async () => {
  try {
    loading.value = true;

    // 过滤掉空值参数
    const filteredParams = Object.entries(searchParams)
      .filter(([key, value]) => value !== '' && value !== null && value !== undefined)
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    const params = {
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize,
      ...filteredParams,
    };

    const response = await getLetterPetitions(params);
    tableData.value = response.items;
    pagination.total = response.total;
  } catch (error) {
    console.error('Failed to fetch letter petitions:', error);
    MessagePlugin.error('获取信件列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchLetterPetitions();
};

// 分页处理
const handlePageChange = (pageInfo: any) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchLetterPetitions();
};
const tableColumns = ref<TableProps['columns']>([
  {
    colKey: 'serial-number',
    title: '序号',
    align: 'center',
  },
  {
    colKey: 'petition_no',
    title: '信件编号',
    align: 'center',
  },
  {
    colKey: 'petitioner_name',
    title: '发信人',
    align: 'center',
  },
  {
    colKey: 'created_at',
    title: '接收时间',
    align: 'center',
    cell: (h, { row }) => {
      return new Date(row.created_at).toLocaleString();
    },
  },
  {
    colKey: 'title',
    title: '信件标题',
    align: 'center',
  },
  {
    colKey: 'priority',
    title: '紧急程度',
    align: 'center',
    cell: (h, { row }) => {
      const priorityMap = {
        0: '普通',
        1: '重要',
        2: '紧急',
        3: '特急',
      };
      return priorityMap[row.priority] || '普通';
    },
  },
  {
    colKey: 'status',
    title: '处理状态',
    align: 'center',
    cell: (h, { row }) => {
      const statusMap = {
        'PENDING': '待处理',
        'PROCESSING': '处理中',
        'COMPLETED': '已完成',
        'CLOSED': '已关闭',
      };
      return statusMap[row.status] || row.status;
    },
  },
  {
    colKey: 'is_sensitive',
    title: '敏感标记',
    align: 'center',
    cell: (h, { row }) => {
      return row.is_sensitive ? '是' : '否';
    },
  },
  {
    colKey: 'operation',
    title: '操作',
    align: 'center',
    cell: (h, { row }) => {
      return h(
        't-button',
        {
          variant: 'text',
          onClick: () => handleDetail(row),
        },
        '详情',
      );
    },
  },
]);

const handleDetail = (row: Petition) => {
  router.push({
    name: 'LetterProcessingDetail',
    params: { id: row.id },
  });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchLetterPetitions();
});
</script>

<template>
  <div class="container">
    <div class="breadcrumb">
      <img src="@/assets/images/homepage-nav.png" alt="" />
      <span>信件处理</span>
    </div>
    <div class="wrapper">
      <div class="table">
        <div class="title">
          <img src="@/assets/images/homepage-schedule.png" alt="" />
          <span>信件管理</span>
        </div>
        <div class="search">
          <div class="input">
            <t-input-adornment>
              <template #append>
                <img
                  style="width: 20px; height: 20px; cursor: pointer"
                  src="@/assets/images/search.png"
                  alt=""
                  @click="handleSearch"
                />
              </template>
              <t-input
                v-model="searchParams.query"
                placeholder="请输入信件编号、发信人或标题"
                @enter="handleSearch"
              />
            </t-input-adornment>
          </div>
          <t-select
            v-model="searchParams.status"
            placeholder="选择状态"
            clearable
            style="width: 120px; margin-left: 10px;"
            @change="handleSearch"
          >
            <t-option value="PENDING" label="待处理" />
            <t-option value="PROCESSING" label="处理中" />
            <t-option value="COMPLETED" label="已完成" />
            <t-option value="CLOSED" label="已关闭" />
          </t-select>
          <t-button @click="router.push({ name: 'LetterProcessingCreate' })">
            <template #icon><add-circle-icon /></template>
            新增信件登录
          </t-button>
        </div>
        <div class="t-table-wrapper">
          <t-table
            row-key="id"
            bordered
            :loading="loading"
            :max-height="tableHeight"
            :data="tableData"
            :columns="tableColumns"
            :pagination="{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showJumper: true,
              showSizer: true,
              pageSizeOptions: [10, 20, 50, 100]
            }"
            @page-change="handlePageChange"
          ></t-table>
        </div>
      </div>
      <div class="receptions">
        <div class="title">
          <img src="@/assets/images/homepage-core.png" alt="" />
          <span>信件处理功能区</span>
        </div>
        <div class="reception">
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/letter-icon1.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">智能分拣与分类</div>
              <div class="subtitle">利用自然语言处理技术，对信件内容进行主题识别、情感分析，并自动分类。</div>
            </div>
          </div>
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/letter-icon2.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">案件判重（比对分析）</div>
              <div class="subtitle">通过AI判重功能，识别重复投诉，加快信件处理效率。</div>
            </div>
          </div>
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/letter-icon3.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">流程标准化与时效监控</div>
              <div class="subtitle">自动跟踪各环节处理时效，超期自动催办，提高信访件处理效率。</div>
            </div>
          </div>
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/letter-icon4.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">OCR文字识别</div>
              <div class="subtitle">对信访材料（图片、PDF）进行OCR识别，提取文字内容，辅助登记处理。</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 10px;

  .breadcrumb {
    width: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    column-gap: 9px;

    img {
      width: 11px;
      height: 14px;
    }

    span {
      font-size: 14px;
      color: #242f57;
    }
  }

  .wrapper {
    width: 100%;
    flex: 1;
    column-gap: 20px;
    display: flex;

    .table {
      flex: 1;
      padding: 20px;
      background: #fff;
      box-shadow: 0 2px 4px 0 rgb(28 41 90 / 4%);
      border-radius: 4px;
      border: 1px solid #eaedf7;
      display: flex;
      flex-direction: column;
      row-gap: 20px;

      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        column-gap: 12px;

        img {
          width: 17px;
          height: 18px;
        }

        span {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 30px;
        }
      }

      .search {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .input {
          flex: 1;
          display: flex;
          justify-content: center;

          :deep(.t-input-adornment) {
            width: 718px;
            border-radius: 21px;
            border: 1px solid #dddfe5;
            background: #f6f8f9;

            .t-input__wrap {
              .t-input {
                height: 42px;
                padding: 0 20px;
                background: #f6f8f9;
                border-radius: 21px 0 0 21px;
                border: none;
              }

              .t-is-focused {
                box-shadow: none;
              }
            }

            .t-input-adornment__append {
              width: 70px;
              height: 42px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #2a5caa;
              border-radius: 21px;
              margin-left: 0;
              cursor: pointer;
            }
          }
        }

        .t-button {
          padding: 0 35px;
          height: 42px;
          background: #2a5caa;
          border-radius: 4px;
        }
      }

      .t-table-wrapper {
        width: 100%;
        flex: 1;
      }
    }

    .receptions {
      flex-shrink: 0;
      height: fit-content;
      background: #fff;
      box-shadow: 0 2px 4px 0 rgb(28 41 90 / 4%);
      border-radius: 4px;
      padding: 20px;

      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        column-gap: 12px;

        img {
          width: 17px;
          height: 18px;
        }

        span {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 30px;
        }
      }

      .reception {
        width: 100%;
        flex: 1;
        margin-top: 20px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 20px;

        .item {
          background: #f0f4f9;
          border-radius: 4px;
          padding: 16px 20px 10px;
          cursor: pointer;

          .jump {
            display: flex;
            align-items: center;
            justify-content: space-between;
            column-gap: 10px;
            margin-bottom: 10px;

            img {
              width: 42px;
              height: 42px;
            }

            .t-icon {
              font-size: 24px;
              color: #2a5caa;
            }
          }

          .introduce {
            width: 153px;
            word-break: break-all;

            .title {
              font-weight: 500;
              font-size: 14px;
              color: #242f57;
              line-height: 20px;
            }

            .subtitle {
              font-weight: 400;
              font-size: 12px;
              color: #61666f;
              line-height: 17px;
              text-align: justify;
              margin-top: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
