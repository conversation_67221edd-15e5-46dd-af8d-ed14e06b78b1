<script lang="tsx">
export default {
  name: 'VisitingReception',
};
</script>

<script lang="tsx" setup>
import { AddCircleIcon, ChevronRightSIcon } from 'tdesign-icons-vue-next';
import { TableProps, MessagePlugin } from 'tdesign-vue-next';
import { ref, onMounted, reactive } from 'vue';

import useTableHeight from '@/composables/useTableHeight';
import router from '@/router';
import { getVisitingPetitions } from '@/api/visiting';
import type { Petition } from '@/api/model/petitionModel';

import HandleDialog from './components/handle.vue';

const statusConfig = {
  PENDING: { theme: 'warning', text: '待处理' },
  PROCESSING: { theme: 'primary', text: '处理中' },
  COMPLETED: { theme: 'success', text: '已处理' },
  CLOSED: { theme: 'default', text: '已关闭' },
};

const { tableHeight } = useTableHeight();

const handleDialogRef = ref();

// 响应式数据
const tableData = ref<Petition[]>([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
});

// 搜索参数
const searchParams = reactive({
  query: '',
});

// 获取来访接待列表数据
const fetchVisitingPetitions = async () => {
  try {
    loading.value = true;
    const params = {
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize,
    };

    const response = await getVisitingPetitions(params);
    tableData.value = response.items;
    pagination.total = response.total;
  } catch (error) {
    console.error('Failed to fetch visiting petitions:', error);
    MessagePlugin.error('获取来访接待列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchVisitingPetitions();
};

// 分页处理
const handlePageChange = (pageInfo: any) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchVisitingPetitions();
};
const tableColumns = ref<TableProps['columns']>([
  {
    colKey: 'serial-number',
    title: '序号',
    align: 'center',
    width: 70,
  },
  {
    colKey: 'petition_no',
    title: '接待编号',
    align: 'center',
  },
  {
    colKey: 'petitioner_name',
    title: '来访人',
    align: 'center',
  },
  {
    colKey: 'created_at',
    title: '来访时间',
    align: 'center',
    cell: (_h, { row }) => {
      return new Date(row.created_at).toLocaleString();
    },
  },
  {
    colKey: 'title',
    title: '诉求标题',
    align: 'center',
  },
  {
    colKey: 'status',
    title: '处理状态',
    align: 'center',
    cell: (_h, { row }) => {
      const config = statusConfig[row.status as keyof typeof statusConfig];
      return <t-tag theme={config.theme} content={config.text} />;
    },
  },
  {
    colKey: 'operation',
    title: '操作',
    align: 'center',
    width: 200,
    cell: (_h, { row }) => (
      <div>
        <t-button
          theme="primary"
          hover="color"
          content="开始处理"
          onClick={() => {
            handleDialogRef.value.visible = true;
          }}
        />
        <t-button
          variant="outline"
          theme="primary"
          hover="color"
          content="查看"
          onClick={() => {
            router.push({ name: 'VisitingReceptionDetail', params: { id: row.id } });
          }}
        />
      </div>
    ),
  },
]);

// 组件挂载时获取数据
onMounted(() => {
  fetchVisitingPetitions();
});
</script>

<template>
  <div class="container">
    <div class="breadcrumb">
      <img src="@/assets/images/homepage-nav.png" alt="" />
      <span>来访接待</span>
    </div>
    <div class="wrapper">
      <div class="table">
        <div class="title">
          <img src="@/assets/images/homepage-schedule.png" alt="" />
          <span>来访接待管理</span>
        </div>
        <div class="search">
          <div class="input">
            <t-input-adornment>
              <template #append>
                <img
                  style="width: 20px; height: 20px; cursor: pointer"
                  src="@/assets/images/search.png"
                  alt=""
                  @click="handleSearch"
                />
              </template>
              <t-input
                v-model="searchParams.query"
                placeholder="请输入接待编号或来访人姓名"
                @enter="handleSearch"
              />
            </t-input-adornment>
          </div>
          <t-button @click="router.push({ name: 'VisitingReceptionCreate' })">
            <template #icon><add-circle-icon /></template>
            新建接待
          </t-button>
        </div>
        <div class="t-table-wrapper">
          <t-table
            row-key="id"
            bordered
            :loading="loading"
            :max-height="tableHeight"
            :data="tableData"
            :columns="tableColumns"
            :pagination="{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showJumper: true,
              showSizer: true,
              pageSizeOptions: [10, 20, 50, 100]
            }"
            @page-change="handlePageChange"
          ></t-table>
        </div>
      </div>
      <div class="receptions">
        <div class="title">
          <img src="@/assets/images/homepage-core.png" alt="" />
          <span>接待功能区</span>
        </div>
        <div class="reception">
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/reception-icon1.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">语音识别与诉求提取</div>
              <div class="subtitle">实时将来访人口述内容转化为文字，自动提取核心诉求。</div>
            </div>
          </div>
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/reception-icon2.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">人证比对</div>
              <div class="subtitle">利用人脸识别技术，进行身份核验，简化登记流程。</div>
            </div>
          </div>
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/reception-icon3.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">政策法规智能推送</div>
              <div class="subtitle">根据诉求关键词，自动关联政策法规，提供参考依据。</div>
            </div>
          </div>
          <div class="item">
            <div class="jump">
              <img src="@/assets/images/reception-icon4.png" alt="" />
              <chevron-right-s-icon />
            </div>
            <div class="introduce">
              <div class="title">情绪识别预警</div>
              <div class="subtitle">根据信访内容实时判断情绪状态，对异常情绪进行预警。</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <handle-dialog ref="handleDialogRef" />
  </div>
</template>
<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;

  .breadcrumb {
    width: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    column-gap: 9px;

    img {
      width: 11px;
      height: 14px;
    }

    span {
      font-size: 14px;
      color: #242f57;
    }
  }

  .wrapper {
    width: 100%;
    flex: 1;
    column-gap: 20px;
    margin-top: 10px;
    display: flex;

    .table {
      flex: 1;
      padding: 20px;
      background: #fff;
      box-shadow: 0 2px 4px 0 rgb(28 41 90 / 4%);
      border-radius: 4px;
      border: 1px solid #eaedf7;
      display: flex;
      flex-direction: column;
      row-gap: 20px;

      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        column-gap: 12px;

        img {
          width: 17px;
          height: 18px;
        }

        span {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 30px;
        }
      }

      .search {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .input {
          flex: 1;
          display: flex;
          justify-content: center;

          :deep(.t-input-adornment) {
            width: 718px;
            border-radius: 21px;
            border: 1px solid #dddfe5;
            background: #f6f8f9;

            .t-input__wrap {
              .t-input {
                height: 42px;
                padding: 0 20px;
                background: #f6f8f9;
                border-radius: 21px 0 0 21px;
                border: none;
              }

              .t-is-focused {
                box-shadow: none;
              }
            }

            .t-input-adornment__append {
              width: 70px;
              height: 42px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #2a5caa;
              border-radius: 21px;
              margin-left: 0;
              cursor: pointer;
            }
          }
        }

        .t-button {
          padding: 0 35px;
          height: 42px;
          border-radius: 4px;
        }
      }

      .t-table-wrapper {
        width: 100%;
        flex: 1;
      }
    }

    .receptions {
      flex-shrink: 0;
      height: fit-content;
      background: #fff;
      box-shadow: 0 2px 4px 0 rgb(28 41 90 / 4%);
      border-radius: 4px;
      padding: 20px;

      .title {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        column-gap: 12px;

        img {
          width: 17px;
          height: 18px;
        }

        span {
          font-weight: 500;
          font-size: 20px;
          color: #242f57;
          line-height: 30px;
        }
      }

      .reception {
        width: 100%;
        flex: 1;
        margin-top: 20px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 20px;

        .item {
          background: #f0f4f9;
          border-radius: 4px;
          padding: 16px 20px 10px;
          cursor: pointer;

          .jump {
            display: flex;
            align-items: center;
            justify-content: space-between;
            column-gap: 10px;
            margin-bottom: 10px;

            img {
              width: 42px;
              height: 42px;
            }

            .t-icon {
              font-size: 24px;
              color: #2a5caa;
            }
          }

          .introduce {
            width: 153px;
            word-break: break-all;

            .title {
              font-weight: 500;
              font-size: 14px;
              color: #242f57;
              line-height: 20px;
            }

            .subtitle {
              font-weight: 400;
              font-size: 12px;
              color: #61666f;
              line-height: 17px;
              text-align: justify;
              margin-top: 10px;
            }
          }
        }
      }
    }
  }
}
</style>
