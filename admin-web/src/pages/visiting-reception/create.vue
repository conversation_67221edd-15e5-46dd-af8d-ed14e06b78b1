<script lang="ts">
export default {
  name: 'VisitingReceptionCerate',
};
</script>

<script lang="ts" setup>
import { CloseIcon, VerifyFilledIcon } from 'tdesign-icons-vue-next';
import { FormProps } from 'tdesign-vue-next';
import { reactive, ref } from 'vue';

import router from '@/router';

const formData = reactive({
  // 身份信息
  idCard: '', // 身份证号
  name: '', // 姓名
  age: null, // 年龄
  address: '', // 住址

  // 诉求信息
  appealType: '', // 诉求类型
  appealDetail: '', // 诉求详情
  keywords: [], // 关键词
  attachments: [], // 附件
  urgencyLevel: '', // 紧急程度
});

const formRules: FormProps['rules'] = {
  idCard: [{ required: true, message: '请输入身份证号' }],
  name: [{ required: true, message: '请输入姓名' }],
  age: [{ required: true, message: '请输入年龄' }],
  address: [{ required: true, message: '请输入住址' }],
  appealType: [{ required: true, message: '请选择诉求类型' }],
  appealDetail: [{ required: true, message: '请输入诉求详情' }],
  urgencyLevel: [{ required: true, message: '请选择紧急程度' }],
};
const filesList = ref([
  {
    name: '这是一个上传成功的文件',
    status: 'success',
    url: 'https://tdesign.gtimg.com/site/source/figma-pc.png',
    size: 1000,
  },
  {
    name: '这是一个上传中的文件',
    status: 'progress',
    percent: 30,
    url: 'https://tdesign.gtimg.com/site/source/figma-pc.png',
    size: 1000,
  },
  {
    name: '这是一个上传失败的文件',
    status: 'fail',
    url: 'https://tdesign.gtimg.com/site/source/figma-pc.png',
    size: 1000,
  },
  {
    name: '这是一个等待上传的文件',
    status: 'waiting',
    url: 'https://tdesign.gtimg.com/site/source/figma-pc.png',
    size: 1000,
  },
]);

const onReset: FormProps['onReset'] = () => {};
const onSubmit: FormProps['onSubmit'] = ({ validateResult, firstError }) => {
  if (validateResult === true) {
    console.log(formData);
  }
};
</script>

<template>
  <div class="container">
    <div class="breadcrumb">
      <img src="@/assets/images/homepage-nav.png" alt="" />
      <span>来访接待 > 新建</span>
    </div>
    <div class="wrapper">
      <div class="title">
        <img src="@/assets/images/reception-create.png" alt="" />
        <span>新建来访接待</span>
      </div>

      <div class="steps">
        <div>
          <span>1</span>
          <span>身份信息</span>
        </div>
        <div>
          <span>2</span>
          <span>诉求登记</span>
        </div>
        <div>
          <span>3</span>
          <span>政策推送</span>
        </div>
        <div>
          <span>4</span>
          <span>处理结果</span>
        </div>
      </div>

      <div class="form">
        <t-form ref="form" :rules="formRules" :data="formData" label-width="100px" @reset="onReset" @submit="onSubmit">
          <t-row :gutter="[20, 25]">
            <t-col :span="12">
              <div class="subtitle">身份信息</div>
            </t-col>
            <t-col :span="12">
              <t-row :gutter="20">
                <t-col :span="3">
                  <t-form-item name="idCard" label="身份证号">
                    <t-input-adornment>
                      <t-input v-model="formData.idCard" placeholder="请输入"></t-input>
                      <template #append>
                        <t-button theme="primary" content="核验" style="margin-left: 10px">
                          <template #icon>
                            <verify-filled-icon />
                          </template>
                        </t-button>
                      </template>
                    </t-input-adornment>
                  </t-form-item>
                </t-col>
                <t-col :span="2">
                  <t-form-item name="name" label="姓名">
                    <t-input v-model="formData.name" placeholder="请输入"></t-input>
                  </t-form-item>
                </t-col>
                <t-col :span="2">
                  <t-form-item name="age" label="年龄">
                    <t-input-number v-model="formData.age" placeholder="请输入" theme="normal"></t-input-number>
                  </t-form-item>
                </t-col>
                <t-col :flex="5">
                  <t-form-item name="address" label="住址">
                    <t-input v-model="formData.address" placeholder="请输入"></t-input>
                  </t-form-item>
                </t-col>
              </t-row>
            </t-col>
            <t-col :span="12">
              <t-form-item label="是否重复来访">
                <t-space align="center">
                  <span class="warning">系统检测: 该访客在过去3个月内有2次来访记录</span>
                  <t-button theme="primary" content="查看历史来访" />
                </t-space>
              </t-form-item>
            </t-col>
            <t-col :span="12"><div class="line"></div></t-col>
            <t-col :span="12">
              <div class="subtitle">诉求登记</div>
            </t-col>
            <t-col :span="12">
              <t-row :gutter="20">
                <t-col :span="3">
                  <t-form-item name="appealType" label="诉求类型">
                    <t-select v-model="formData.appealType" placeholder="请选择" clearable></t-select>
                  </t-form-item>
                </t-col>
              </t-row>
            </t-col>
            <t-col :span="12">
              <t-row :gutter="20">
                <t-col :span="7">
                  <t-form-item name="appealDetail" label="诉求详情">
                    <t-textarea
                      v-model="formData.appealDetail"
                      placeholder="请输入"
                      :autosize="{ minRows: 3, maxRows: 5 }"
                    ></t-textarea>
                  </t-form-item>
                </t-col>
              </t-row>
            </t-col>
            <t-col :span="12">
              <t-row :gutter="20">
                <t-col :span="12">
                  <t-form-item label="诉求关键词（系统自动提取）" label-width="225px">
                    <t-space>
                      <t-tag variant="light" theme="primary">标签一</t-tag>
                      <t-tag variant="light" theme="primary">标签一</t-tag>
                      <t-tag variant="light" theme="primary">标签一</t-tag>
                      <t-tag variant="light" theme="primary">标签一</t-tag>
                    </t-space>
                  </t-form-item>
                </t-col>
              </t-row>
            </t-col>
            <t-col :span="12">
              <t-row :gutter="20">
                <t-col :span="12">
                  <t-form-item label="上传附件">
                    <t-space align="center">
                      <t-upload
                        ref="uploadRef"
                        v-model="formData.attachments"
                        action="https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo"
                        :headers="{ a: 'N1', b: 'N2' }"
                        multiple
                        auto-upload
                        upload-all-files-in-one-request
                        is-batch-upload
                        :size-limit="{ size: 100, unit: 'MB' }"
                        :max="5"
                        :allow-upload-duplicate-file="true"
                      >
                      </t-upload>
                      <t-space>
                        <div v-for="file in filesList" :key="file.name">
                          {{ file.name }}（{{ file.size }} B）
                          <close-icon class="t-upload__icon-delete" />
                        </div>
                      </t-space>
                    </t-space>
                  </t-form-item>
                </t-col>
              </t-row>
            </t-col>
            <t-col :span="12">
              <t-row :gutter="20">
                <t-col :span="3">
                  <t-form-item name="urgencyLevel" label="紧急程度">
                    <t-select v-model="formData.urgencyLevel" placeholder="请选择" clearable></t-select>
                  </t-form-item>
                </t-col>
              </t-row>
            </t-col>
          </t-row>
        </t-form>
      </div>
      <div class="submit">
        <t-button content="取 消" theme="default" @click="router.back()" />
        <t-button content="提 交" theme="primary" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  row-gap: 10px;

  .breadcrumb {
    width: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    column-gap: 9px;

    img {
      width: 11px;
      height: 14px;
    }

    span {
      font-size: 14px;
      color: #242f57;
    }
  }

  .wrapper {
    width: 100%;
    flex: 1;
    background: #fff;
    box-shadow: 0 2px 4px 0 rgb(28 41 90 / 4%);
    border-radius: 4px;
    border: 1px solid #eaedf7;
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: #f0f4f9;
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background-color: rgba(#2a5caa, 0.5);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-button {
      display: none;
      width: 0;
      height: 0;
    }

    .title {
      width: 100%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      column-gap: 10px;

      span {
        font-weight: 500;
        font-size: 20px;
        color: #242f57;
        line-height: 30px;
      }
    }

    .steps {
      width: 100%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 16px;

      > div {
        width: 182px;
        height: 42px;
        display: flex;
        align-items: center;
        padding-left: 35px;
        column-gap: 15px;
        background: url('@/assets/images/reception-step.png') no-repeat center / 100%;
        font-weight: 500;
        font-size: 16px;
        color: #fff;
        line-height: 22px;

        > span:nth-of-type(1) {
          width: 22px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-radius: 50%;
          border: 1px solid #fff;
        }
      }
    }

    .form {
      width: 100%;
      flex: 1;
      padding: 0 40px;
      overflow-x: hidden;
      overflow-y: auto;
      margin-top: 20px;

      :deep(.t-form) {
        .t-form__label {
          font-size: 14px;
          color: #353637;
          padding-right: 10px;
        }

        .warning {
          color: #dd1f1f;
          font-size: 14px;
          font-weight: 500;
        }

        .line {
          height: 1px;
          background: #c9d3de;
        }
      }

      .subtitle {
        font-weight: 500;
        font-size: 16px;
        color: #242f57;
        line-height: 22px;
      }
    }

    .submit {
      width: 100%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
    }
  }
}
</style>
