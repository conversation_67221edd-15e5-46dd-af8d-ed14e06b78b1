<script lang="ts" setup>
import { ref } from 'vue';

const visible = ref(false);

defineExpose({
  visible,
});
</script>

<template>
  <div class="dialog">
    <t-dialog v-model:visible="visible" header="开始处理来访接待" width="1000" confirm-btn="确认开始处理">
      <div class="dialog-wrapper">
        <div>
          <span class="title">来访信息</span>
          <t-descriptions bordered :column="3" table-layout="auto">
            <t-descriptions-item label="来访编号">LF20250427003</t-descriptions-item>
            <t-descriptions-item label="来访人">周继纲</t-descriptions-item>
            <t-descriptions-item label="来访时间">2025-04-27 14:00</t-descriptions-item>
            <t-descriptions-item label="诉求类型">劳资纠纷</t-descriptions-item>
          </t-descriptions>
        </div>
        <div>
          <span class="title">处理人员</span>
          <t-select style="width: 200px"></t-select>
        </div>
        <div>
          <span class="title">备注</span>
          <t-textarea :autosize="{ minRows: 3, maxRows: 5 }"></t-textarea>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<style lang="less" scoped>
.dialog {
  .dialog-wrapper {
    display: flex;
    flex-direction: column;
    row-gap: 20px;

    .title {
      display: inline-block;
      font-weight: 600;
      font-size: 16px;
      color: #242f57;
      line-height: 22px;
      margin-bottom: 5px;
    }
  }
}
</style>
