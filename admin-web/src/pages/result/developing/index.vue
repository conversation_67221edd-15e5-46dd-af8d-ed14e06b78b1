<template>
  <result title="功能开发中" tip="该功能正在开发中，敬请期待" type="developing">
    <div class="result-slot-container">
      <t-button theme="primary" @click="() => $router.push('/')">回到首页</t-button>
      <div class="developing-container">
        <div class="developing-info">
          <div class="title">预计上线时间</div>
          <div class="time">2025年6月30日</div>
          <div class="progress-info">
            <span>开发进度</span>
            <span>50%</span>
          </div>
          <t-progress :percentage="50" :color="{ from: '#00A870', to: '#2A5CAA' }" />
          <div class="feature-list">
            <div class="subtitle">即将上线功能</div>
            <t-space>
              <t-tag theme="primary" variant="light">信息检索</t-tag>
              <t-tag theme="primary" variant="light">智能搜索</t-tag>
              <t-tag theme="primary" variant="light">网络信访</t-tag>
              <t-tag theme="primary" variant="light">数据分析</t-tag>
            </t-space>
          </div>
        </div>
      </div>
    </div>
  </result>
</template>

<script lang="ts">
export default {
  name: 'ResultDeveloping',
};
</script>

<script setup lang="ts">
import Result from '@/components/result/index.vue';
</script>

<style lang="less" scoped>
.result-slot-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  color: var(--td-text-color-secondary);
  height: 80vh;
  max-height: 400px;
}

.developing-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--td-comp-paddingTB-xl) var(--td-comp-paddingLR-xxl);
  width: 640px;
  background: var(--td-bg-color-container);
  box-shadow: 0 1px 2px rgb(0 0 0 / 10%);
  border-radius: var(--td-radius-medium);
  margin-top: 24px;

  .developing-info {
    width: 100%;

    .title {
      font-size: 16px;
      color: var(--td-text-color-primary);
      margin-bottom: 8px;
    }

    .time {
      font-size: 24px;
      color: var(--td-text-color-primary);
      font-weight: bold;
      margin-bottom: 24px;
    }

    .progress-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      color: var(--td-text-color-primary);
    }

    .feature-list {
      margin-top: 24px;

      .subtitle {
        font-size: 16px;
        color: var(--td-text-color-primary);
        margin-bottom: 16px;
      }
    }
  }
}
</style>
