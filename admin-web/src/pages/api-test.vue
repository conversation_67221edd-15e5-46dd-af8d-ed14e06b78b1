<template>
  <div class="api-test-container">
    <h1>API 测试页面</h1>

    <div class="test-section">
      <h2>认证测试</h2>
      <div class="test-item">
        <t-button @click="testLogin" :loading="loginLoading">测试登录</t-button>
        <span v-if="loginResult" :class="loginResult.success ? 'success' : 'error'">
          {{ loginResult.message }}
        </span>
      </div>

      <div class="test-item">
        <t-button @click="testGetUserInfo" :loading="userInfoLoading">获取用户信息</t-button>
        <span v-if="userInfoResult" :class="userInfoResult.success ? 'success' : 'error'">
          {{ userInfoResult.message }}
        </span>
      </div>
    </div>

    <div class="test-section">
      <h2>信件处理 API 测试</h2>
      <div class="test-item">
        <t-button @click="testGetLetters" :loading="lettersLoading">获取信件列表</t-button>
        <span v-if="lettersResult" :class="lettersResult.success ? 'success' : 'error'">
          {{ lettersResult.message }}
        </span>
      </div>
    </div>

    <div class="test-section">
      <h2>来访接待 API 测试</h2>
      <div class="test-item">
        <t-button @click="testGetVisiting" :loading="visitingLoading">获取来访列表</t-button>
        <span v-if="visitingResult" :class="visitingResult.success ? 'success' : 'error'">
          {{ visitingResult.message }}
        </span>
      </div>
    </div>

    <div class="test-section">
      <h2>统计分析 API 测试</h2>
      <div class="test-item">
        <t-button @click="testGetStatistics" :loading="statisticsLoading">获取统计数据</t-button>
        <span v-if="statisticsResult" :class="statisticsResult.success ? 'success' : 'error'">
          {{ statisticsResult.message }}
        </span>
      </div>
    </div>

    <div class="test-section">
      <h2>API 响应日志</h2>
      <div class="log-container">
        <pre>{{ apiLogs }}</pre>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { login, getCurrentUserInfo } from '@/api/auth';
import { getLetterPetitions } from '@/api/letter';
import { getVisitingPetitions } from '@/api/visiting';
import { getCardList } from '@/api/statistics';

// 测试结果状态
const loginLoading = ref(false);
const loginResult = ref<{ success: boolean; message: string } | null>(null);

const userInfoLoading = ref(false);
const userInfoResult = ref<{ success: boolean; message: string } | null>(null);

const lettersLoading = ref(false);
const lettersResult = ref<{ success: boolean; message: string } | null>(null);

const visitingLoading = ref(false);
const visitingResult = ref<{ success: boolean; message: string } | null>(null);

const statisticsLoading = ref(false);
const statisticsResult = ref<{ success: boolean; message: string } | null>(null);

const apiLogs = ref('');

// 添加日志
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleString();
  apiLogs.value += `[${timestamp}] ${message}\n`;
};

// 测试登录
const testLogin = async () => {
  loginLoading.value = true;
  try {
    const response = await login({
      username: 'admin',
      password: 'adminadmin',
    });

    loginResult.value = {
      success: true,
      message: `登录成功，Token: ${response.access_token.substring(0, 20)}...`,
    };

    addLog(`登录测试成功: ${JSON.stringify(response)}`);
    MessagePlugin.success('登录测试成功');
  } catch (error) {
    loginResult.value = {
      success: false,
      message: `登录失败: ${error.message || error}`,
    };

    addLog(`登录测试失败: ${error}`);
    MessagePlugin.error('登录测试失败');
  } finally {
    loginLoading.value = false;
  }
};

// 测试获取用户信息
const testGetUserInfo = async () => {
  userInfoLoading.value = true;
  try {
    const response = await getCurrentUserInfo();

    userInfoResult.value = {
      success: true,
      message: `获取用户信息成功: ${response.username}`,
    };

    addLog(`获取用户信息成功: ${JSON.stringify(response)}`);
    MessagePlugin.success('获取用户信息成功');
  } catch (error) {
    userInfoResult.value = {
      success: false,
      message: `获取用户信息失败: ${error.message || error}`,
    };

    addLog(`获取用户信息失败: ${error}`);
    MessagePlugin.error('获取用户信息失败');
  } finally {
    userInfoLoading.value = false;
  }
};

// 测试获取信件列表
const testGetLetters = async () => {
  lettersLoading.value = true;
  try {
    const response = await getLetterPetitions({ limit: 5 });

    lettersResult.value = {
      success: true,
      message: `获取信件列表成功，共 ${response.total} 条记录`,
    };

    addLog(`获取信件列表成功: ${JSON.stringify(response)}`);
    MessagePlugin.success('获取信件列表成功');
  } catch (error) {
    lettersResult.value = {
      success: false,
      message: `获取信件列表失败: ${error.message || error}`,
    };

    addLog(`获取信件列表失败: ${error}`);
    MessagePlugin.error('获取信件列表失败');
  } finally {
    lettersLoading.value = false;
  }
};

// 测试获取来访列表
const testGetVisiting = async () => {
  visitingLoading.value = true;
  try {
    const response = await getVisitingPetitions({ limit: 5 });

    visitingResult.value = {
      success: true,
      message: `获取来访列表成功，共 ${response.total} 条记录`,
    };

    addLog(`获取来访列表成功: ${JSON.stringify(response)}`);
    MessagePlugin.success('获取来访列表成功');
  } catch (error) {
    visitingResult.value = {
      success: false,
      message: `获取来访列表失败: ${error.message || error}`,
    };

    addLog(`获取来访列表失败: ${error}`);
    MessagePlugin.error('获取来访列表失败');
  } finally {
    visitingLoading.value = false;
  }
};

// 测试获取统计数据
const testGetStatistics = async () => {
  statisticsLoading.value = true;
  try {
    const response = await getCardList();

    statisticsResult.value = {
      success: true,
      message: '获取统计数据成功',
    };

    addLog(`获取统计数据成功: ${JSON.stringify(response)}`);
    MessagePlugin.success('获取统计数据成功');
  } catch (error) {
    statisticsResult.value = {
      success: false,
      message: `获取统计数据失败: ${error.message || error}`,
    };

    addLog(`获取统计数据失败: ${error}`);
    MessagePlugin.error('获取统计数据失败');
  } finally {
    statisticsLoading.value = false;
  }
};
</script>

<style lang="less" scoped>
.api-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;

    h2 {
      margin-bottom: 15px;
      color: #555;
      border-bottom: 2px solid #007bff;
      padding-bottom: 5px;
    }

    .test-item {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 10px;

      .success {
        color: #28a745;
        font-weight: 500;
      }

      .error {
        color: #dc3545;
        font-weight: 500;
      }
    }
  }

  .log-container {
    max-height: 300px;
    overflow-y: auto;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;

    pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}
</style>
