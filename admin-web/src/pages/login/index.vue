<script lang="ts" setup>
import { LockOnIcon, User1FilledIcon } from 'tdesign-icons-vue-next';
import { FormProps, MessagePlugin } from 'tdesign-vue-next';
import { reactive, ref } from 'vue';

import router from '@/router';
import { useUserStore } from '@/store';

const userStore = useUserStore();

const loading = ref(false);
const formData: FormProps['data'] = reactive({
  account: 'admin',
  password: 'admin',
  remember: true,
});

const rules: FormProps['rules'] = {
  account: [
    {
      required: true,
      message: '请输入账号',
      trigger: 'blur',
    },
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: 'blur',
    },
  ],
};

const onReset: FormProps['onReset'] = () => {
  MessagePlugin.success('重置成功');
};
const onSubmit: FormProps['onSubmit'] = async ({ validateResult }) => {
  if (validateResult === true) {
    try {
      loading.value = true;
      await userStore.onLogin(formData);
      router.push('/');
    } finally {
      loading.value = false;
    }
  }
};
</script>

<template>
  <div class="container">
    <div class="wrapper">
      <div class="info">
        <img class="logo" src="@/assets/images/logo.png" alt="" />
        <div class="title">公安信访智能化系统</div>
        <div class="subtitle">Intelligent Public Security Petition System</div>
        <div class="introduce">
          欢迎使用公安信访智能化系统，本系统利用人工智能技术提升公安信访工作的效率和质量，通过智能化手段辅助信访案件的接收、分类、处理和分析。
        </div>
        <div class="advantages">
          <div class="advantage">
            <img src="@/assets/images/login-icon1.png" alt="" />
            <span>语音识别与诉求提取</span>
          </div>
          <div class="advantage">
            <img src="@/assets/images/login-icon2.png" alt="" />
            <span>人证比对与身份核验</span>
          </div>
          <div class="advantage">
            <img src="@/assets/images/login-icon3.png" alt="" />
            <span>信访风险智能预测</span>
          </div>
          <div class="advantage">
            <img src="@/assets/images/login-icon4.png" alt="" />
            <span>智能问答与助手</span>
          </div>
        </div>
      </div>
      <div class="login">
        <div class="tip">用户登录</div>
        <t-form
          ref="form"
          :data="formData"
          :rules="rules"
          label-align="top"
          :required-mark="false"
          @reset="onReset"
          @submit="onSubmit"
        >
          <t-form-item name="account" label="账号">
            <t-input v-model="formData.account" clearable placeholder="请输入">
              <template #prefix-icon>
                <user1-filled-icon />
              </template>
            </t-input>
          </t-form-item>

          <t-form-item name="password" label="密码">
            <t-input v-model="formData.password" type="password" clearable placeholder="请输入">
              <template #prefix-icon>
                <lock-on-icon />
              </template>
            </t-input>
          </t-form-item>
          <t-form-item name="remember">
            <div class="remember">
              <t-checkbox v-model="formData.remember">记住我</t-checkbox>
              <span class="forget">忘记密码？</span>
            </div>
          </t-form-item>

          <t-form-item>
            <t-button :disabled="loading" theme="primary" type="submit" block>登 录</t-button>
          </t-form-item>
        </t-form>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  background: url('@/assets/images/login-bg.png') no-repeat center / 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .wrapper {
    width: 880px;
    padding: 50px 60px 40px;
    background: rgb(255 255 255 / 80%);
    box-shadow:
      0 12px 20px 0 rgb(0 50 120 / 16%),
      inset 0 2px 4px 0 #fff;
    border-radius: 16px;
    display: flex;
    // align-items: center;
    justify-content: center;
    column-gap: 35px;

    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;

      .logo {
        width: 51px;
        height: 54px;
      }

      .title {
        width: 100%;
        margin-top: 13px;
        font-size: 28px;
        color: #17191c;
        line-height: 33px;
        letter-spacing: 2px;
        text-align: center;
      }

      .subtitle {
        width: 100%;
        margin-top: 7px;
        font-family: Arial, sans-serif;
        font-size: 14px;
        color: rgb(23 25 28 / 50%);
        line-height: 16px;
        text-align: center;
      }

      .introduce {
        width: 100%;
        margin-top: 28px;
        font-size: 16px;
        color: #242f57;
        line-height: 28px;
        letter-spacing: 1px;
        text-align: justify;
      }

      .advantages {
        width: 100%;
        margin-top: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 20px;

        .advantage {
          width: 100%;
          padding-left: 60px;
          display: flex;
          align-items: center;
          column-gap: 10px;

          img {
            width: 34px;
            height: 34px;
          }

          span {
            font-weight: 400;
            font-size: 14px;
            color: #242f57;
            line-height: 20px;
          }
        }
      }
    }

    .login {
      flex: 1;

      .tip {
        font-weight: 600;
        font-size: 20px;
        color: #17191c;
        // line-height: 28px;
        letter-spacing: 1px;
        margin-top: 70px;
      }

      :deep(.t-form) {
        margin-top: 30px;

        .t-input__wrap {
          width: 100%;
          height: 54px;
          line-height: 54px;
          border-radius: 4px;

          .t-input {
            width: 100%;
            height: 100%;
            background: #f3f4f8;
            border: 1px solid transparent;
            font-size: 16px;

            .t-input__inner {
              font-size: 16px;

              &::placeholder {
                color: #999;
                font-size: 16px;
              }
            }
          }

          .t-is-focused {
            box-shadow: none;
            border: 1px solid #176fea;
          }
        }

        .t-button {
          height: 54px;
          background: #2a5caa;
          border: unset;
          border-radius: 6px;
          font-size: 18px;
          color: #fff;
          margin-top: 36px;
        }

        .remember {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .t-checkbox {
            color: #61666f;

            .t-checkbox__input {
              border-color: #2a5caa;
              background-color: #2a5caa;
            }
          }

          .forget {
            font-size: 16px;
            color: #2a5caa;
            line-height: 22px;
          }
        }
      }
    }
  }
}
</style>
