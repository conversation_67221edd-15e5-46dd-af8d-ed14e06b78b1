/**
 * 来访接待相关API
 */
import { request } from '@/utils/request';
import type { Petition, PetitionList, PetitionCreate, PetitionUpdate } from '@/api/model/petitionModel';

const API_PREFIX = '/visiting';

export interface VisitingPetitionParams {
  skip?: number;
  limit?: number;
}

export interface StatusUpdateParams {
  status: string;
  note?: string;
}

/**
 * 获取来访接待列表
 */
export function getVisitingPetitions(params?: VisitingPetitionParams) {
  return request.get<PetitionList>({
    url: API_PREFIX,
    params,
  });
}

/**
 * 获取来访接待详情
 */
export function getVisitingPetitionDetail(id: string | number) {
  return request.get<Petition>({
    url: `${API_PREFIX}/${id}`,
  });
}

/**
 * 创建来访接待
 */
export function createVisitingPetition(data: PetitionCreate) {
  return request.post<Petition>({
    url: API_PREFIX,
    data,
  });
}

/**
 * 更新来访接待信息
 */
export function updateVisitingPetition(id: string | number, data: PetitionUpdate) {
  return request.put<Petition>({
    url: `${API_PREFIX}/${id}`,
    data,
  });
}

/**
 * 更新来访接待状态
 */
export function updateVisitingPetitionStatus(id: string | number, statusData: StatusUpdateParams) {
  return request.put({
    url: `${API_PREFIX}/${id}/status`,
    data: statusData,
  });
}

/**
 * 分配来访接待处理人
 */
export function assignVisitingPetitionHandler(id: string | number, handlerId: number) {
  return request.put({
    url: `${API_PREFIX}/${id}/assign`,
    data: { handler_id: handlerId },
  });
}
