/**
 * 文件上传相关API
 */
import { request } from '@/utils/request';

export interface UploadResult {
  filename: string;
  unique_filename: string;
  file_path: string;
  file_size: number;
  upload_time: string;
  uploaded_by: string;
}

export interface MultipleUploadResult {
  uploaded_files: UploadResult[];
  failed_files: Array<{
    filename: string;
    error: string;
  }>;
  total_uploaded: number;
  total_failed: number;
}

/**
 * 上传单个文件
 */
export function uploadFile(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request.post<UploadResult>({
    url: '/upload/upload',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 批量上传文件
 */
export function uploadMultipleFiles(files: File[]) {
  const formData = new FormData();
  files.forEach(file => {
    formData.append('files', file);
  });
  
  return request.post<MultipleUploadResult>({
    url: '/upload/upload-multiple',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 下载文件
 */
export function downloadFile(filename: string) {
  return request.get({
    url: `/upload/download/${filename}`,
    responseType: 'blob',
  });
}

/**
 * 删除文件
 */
export function deleteFile(filename: string) {
  return request.delete({
    url: `/upload/delete/${filename}`,
  });
}
