/**
 * 在线信访相关API
 */
import { request } from '@/utils/request';
import type { Petition, PetitionList, PetitionCreate, PetitionUpdate } from '@/api/model/petitionModel';

const API_PREFIX = '/online';

export interface OnlinePetitionParams {
  skip?: number;
  limit?: number;
}

export interface StatusUpdateParams {
  status: string;
  note?: string;
}

/**
 * 获取在线信访列表
 */
export function getOnlinePetitions(params?: OnlinePetitionParams) {
  return request.get<PetitionList>({
    url: API_PREFIX,
    params,
  });
}

/**
 * 获取在线信访详情
 */
export function getOnlinePetitionDetail(id: string | number) {
  return request.get<Petition>({
    url: `${API_PREFIX}/${id}`,
  });
}

/**
 * 创建在线信访
 */
export function createOnlinePetition(data: PetitionCreate) {
  return request.post<Petition>({
    url: API_PREFIX,
    data,
  });
}

/**
 * 更新在线信访信息
 */
export function updateOnlinePetition(id: string | number, data: PetitionUpdate) {
  return request.put<Petition>({
    url: `${API_PREFIX}/${id}`,
    data,
  });
}

/**
 * 更新在线信访状态
 */
export function updateOnlinePetitionStatus(id: string | number, statusData: StatusUpdateParams) {
  return request.put({
    url: `${API_PREFIX}/${id}/status`,
    data: statusData,
  });
}

/**
 * 分配在线信访处理人
 */
export function assignOnlinePetitionHandler(id: string | number, handlerId: number) {
  return request.put({
    url: `${API_PREFIX}/${id}/assign`,
    data: { handler_id: handlerId },
  });
}
