/**
 * 认证相关API
 */
import { request } from '@/utils/request';

export interface LoginParams {
  username: string;
  password: string;
}

export interface LoginResult {
  access_token: string;
  token_type: string;
}

export interface UserInfo {
  id: number;
  username: string;
  email?: string;
  full_name?: string;
  phone?: string;
  is_active: boolean;
  is_superuser: boolean;
  roles: Array<{
    id: number;
    name: string;
    description?: string;
  }>;
}

/**
 * 用户登录
 */
export function login(data: LoginParams) {
  const formData = new FormData();
  formData.append('username', data.username);
  formData.append('password', data.password);
  
  return request.post<LoginResult>({
    url: '/auth/login',
    data: formData,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 测试token有效性
 */
export function testToken() {
  return request.post<UserInfo>({
    url: '/auth/test-token',
  });
}

/**
 * 获取当前用户信息
 */
export function getCurrentUserInfo() {
  return request.get<UserInfo>({
    url: '/users/me',
  });
}

/**
 * 更新当前用户信息
 */
export function updateCurrentUserInfo(data: Partial<UserInfo>) {
  return request.put<UserInfo>({
    url: '/users/me',
    data,
  });
}

/**
 * 获取用户列表（仅超级管理员）
 */
export function getUsers(params?: { skip?: number; limit?: number }) {
  return request.get<{ total: number; items: UserInfo[] }>({
    url: '/users',
    params,
  });
}

/**
 * 创建用户（仅超级管理员）
 */
export function createUser(data: {
  username: string;
  password: string;
  email?: string;
  full_name?: string;
  phone?: string;
  is_active?: boolean;
  is_superuser?: boolean;
}) {
  return request.post<UserInfo>({
    url: '/users',
    data,
  });
}

/**
 * 通过ID获取用户信息（仅超级管理员）
 */
export function getUserById(userId: number) {
  return request.get<UserInfo>({
    url: `/users/${userId}`,
  });
}

/**
 * 更新用户信息（仅超级管理员）
 */
export function updateUserById(userId: number, data: Partial<UserInfo>) {
  return request.put<UserInfo>({
    url: `/users/${userId}`,
    data,
  });
}

/**
 * 删除用户（仅超级管理员）
 */
export function deleteUserById(userId: number) {
  return request.delete({
    url: `/users/${userId}`,
  });
}
