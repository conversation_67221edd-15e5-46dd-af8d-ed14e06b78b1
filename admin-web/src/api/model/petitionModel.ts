/**
 * 信访相关数据模型
 */

// 处理记录
export interface ProcessingRecord {
  id: number;
  petition_id: number;
  handler_id: number;
  action: string;
  note?: string;
  created_at: string;
}

// 信件特定信息
export interface LetterInfo {
  received_date: string;
  letter_source?: string;
  has_attachments?: boolean;
  attachment_description?: string;
}

// 来访特定信息
export interface VisitingInfo {
  reception_time: string;
  reception_location?: string;
  visitor_count?: number;
  is_group_visit?: boolean;
  has_id_verified?: boolean;
}

// 在线特定信息
export interface OnlineInfo {
  ip_address?: string;
  platform?: string;
  attachment_urls?: string;
}

// 信访记录
export interface Petition {
  id: number;
  petition_no: string;
  type: string;
  title: string;
  content?: string;
  petitioner_name: string;
  petitioner_phone?: string;
  petitioner_id_card?: string;
  petitioner_address?: string;
  priority?: number;
  is_sensitive?: boolean;
  status: string;
  created_at: string;
  updated_at: string;
  handler_id?: number;
  visiting_info?: VisitingInfo;
  online_info?: OnlineInfo;
  letter_info?: LetterInfo;
  processing_records?: ProcessingRecord[];
}

// 创建信访数据
export interface PetitionCreate {
  title: string;
  content?: string;
  type: string;
  petitioner_name: string;
  petitioner_phone?: string;
  petitioner_id_card?: string;
  petitioner_address?: string;
  priority?: number;
  is_sensitive?: boolean;
  visiting_info?: VisitingPetitionCreate;
  online_info?: OnlinePetitionCreate;
  letter_info?: LetterPetitionCreate;
}

// 更新信访数据
export interface PetitionUpdate {
  title?: string;
  content?: string;
  petitioner_name?: string;
  petitioner_phone?: string;
  petitioner_address?: string;
  priority?: number;
  is_sensitive?: boolean;
  status?: string;
  handler_id?: number;
  visiting_info?: VisitingPetitionUpdate;
  online_info?: OnlinePetitionUpdate;
  letter_info?: LetterPetitionUpdate;
}

// 信件创建数据
export interface LetterPetitionCreate {
  received_date: string;
  letter_source?: string;
  has_attachments?: boolean;
  attachment_description?: string;
}

// 信件更新数据
export interface LetterPetitionUpdate {
  received_date?: string;
  letter_source?: string;
  has_attachments?: boolean;
  attachment_description?: string;
}

// 来访创建数据
export interface VisitingPetitionCreate {
  reception_time: string;
  reception_location?: string;
  visitor_count?: number;
  is_group_visit?: boolean;
  has_id_verified?: boolean;
}

// 来访更新数据
export interface VisitingPetitionUpdate {
  reception_time?: string;
  reception_location?: string;
  visitor_count?: number;
  is_group_visit?: boolean;
  has_id_verified?: boolean;
}

// 在线创建数据
export interface OnlinePetitionCreate {
  ip_address?: string;
  platform?: string;
  attachment_urls?: string;
}

// 在线更新数据
export interface OnlinePetitionUpdate {
  ip_address?: string;
  platform?: string;
  attachment_urls?: string;
}

// 信访列表响应
export interface PetitionList {
  total: number;
  items: Petition[];
}

// 兼容旧接口
export interface PetitionDetail extends Petition {}
export interface PetitionBase extends Petition {}
