/**
 * 统计分析相关API
 */
import { request } from '@/utils/request';

export interface DailyUpdate {
  id: number;
  update_date: string;
  total_petition_count: number;
  visiting_petition_count: number;
  online_petition_count: number;
  letter_petition_count: number;
  pending_petition_count: number;
  processed_petition_count: number;
  average_processing_time: number;
  sensitive_petition_count: number;
  group_visiting_count: number;
  created_at: string;
  updated_at: string;
}

export interface DailyUpdateList {
  total: number;
  items: DailyUpdate[];
}

export interface DataAnalysis {
  id: number;
  analysis_date: string;
  analysis_type: string;
  analysis_dimension: string;
  analysis_result: string;
  created_at: string;
  updated_at: string;
}

/**
 * 获取基础列表数据
 */
export function getList() {
  return request.get({
    url: '/statistics/get-list',
  });
}

/**
 * 获取卡片列表数据
 */
export function getCardList() {
  return request.get({
    url: '/statistics/get-card-list',
  });
}

/**
 * 获取每日更新列表
 */
export function getDailyUpdates(params?: { skip?: number; limit?: number }) {
  return request.get<DailyUpdateList>({
    url: '/statistics/daily-updates',
    params,
  });
}

/**
 * 获取今日更新数据
 */
export function getTodayUpdate() {
  return request.get<DailyUpdate>({
    url: '/statistics/daily-updates/today',
  });
}

/**
 * 获取特定类型和维度的数据分析
 */
export function getDataAnalysis(analysisType: string, dimension: string) {
  return request.get<DataAnalysis>({
    url: `/statistics/data-analysis/${analysisType}/${dimension}`,
  });
}

/**
 * 获取项目列表数据
 */
export function getProjectList() {
  return request.get({
    url: '/petitions/get-project-list',
  });
}

/**
 * 获取购买列表数据
 */
export function getPurchaseList() {
  return request.get({
    url: '/petitions/get-purchase-list',
  });
}
