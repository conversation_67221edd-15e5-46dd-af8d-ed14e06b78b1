/**
 * 通用信访管理相关API
 */
import { request } from '@/utils/request';
import type { Petition, PetitionList } from '@/api/model/petitionModel';

export interface PetitionParams {
  skip?: number;
  limit?: number;
  type?: string;
  status?: string;
  query?: string;
  handler_id?: number;
  is_sensitive?: boolean;
  start_date?: string;
  end_date?: string;
}

/**
 * 获取信访记录列表，支持多条件筛选
 */
export function getPetitions(params?: PetitionParams) {
  return request.get<PetitionList>({
    url: '/petitions',
    params,
  });
}

/**
 * 获取信访记录详情
 */
export function getPetition(petitionId: number) {
  return request.get<Petition>({
    url: `/petitions/${petitionId}`,
  });
}

/**
 * 更新信访记录状态
 */
export function updatePetitionStatus(petitionId: number, status: string) {
  return request.put<Petition>({
    url: `/petitions/${petitionId}/status`,
    params: { status },
  });
}

/**
 * 分配信访记录处理人
 */
export function assignHandler(petitionId: number, handlerId: number) {
  return request.put<Petition>({
    url: `/petitions/${petitionId}/assign`,
    params: { handler_id: handlerId },
  });
}
