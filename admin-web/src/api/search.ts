/**
 * 智能搜索相关API
 */
import { request } from '@/utils/request';

export interface SearchParams {
  query: string;
  skip?: number;
  limit?: number;
}

export interface SearchSuggestionsParams {
  query: string;
}

/**
 * 智能搜索信访记录
 */
export function searchPetitions(params: SearchParams) {
  return request.get({
    url: '/search',
    params,
  });
}

/**
 * 获取搜索建议
 */
export function getSearchSuggestions(params: SearchSuggestionsParams) {
  return request.get({
    url: '/search/suggestions',
    params,
  });
}
