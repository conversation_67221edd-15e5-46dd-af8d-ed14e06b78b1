/**
 * 信件处理相关API
 */
import { request } from '@/utils/request';
import type { Petition, PetitionList, PetitionCreate, PetitionUpdate } from '@/api/model/petitionModel';

const API_PREFIX = '/letter/letters';

export interface LetterPetitionParams {
  skip?: number;
  limit?: number;
  status?: string;
  query?: string;
  start_date?: string;
  end_date?: string;
}

export interface StatusUpdateParams {
  status: string;
  note?: string;
}

/**
 * 获取信件列表
 */
export function getLetterPetitions(params?: LetterPetitionParams) {
  return request.get<PetitionList>({
    url: API_PREFIX,
    params,
  });
}

/**
 * 获取信件详情
 */
export function getLetterPetitionDetail(id: string | number) {
  return request.get<Petition>({
    url: `${API_PREFIX}/${id}`,
  });
}

/**
 * 创建信件信访
 */
export function createLetterPetition(data: PetitionCreate) {
  return request.post<Petition>({
    url: API_PREFIX,
    data,
  });
}

/**
 * 更新信件信息
 */
export function updateLetterPetition(id: string | number, data: PetitionUpdate) {
  return request.put<Petition>({
    url: `${API_PREFIX}/${id}`,
    data,
  });
}

/**
 * 更新信件状态
 */
export function updateLetterPetitionStatus(id: string | number, statusData: StatusUpdateParams) {
  return request.put({
    url: `${API_PREFIX}/${id}/status`,
    data: statusData,
  });
}

/**
 * 分配信件处理人
 */
export function assignLetterPetitionHandler(id: string | number, handlerId: number) {
  return request.put({
    url: `${API_PREFIX}/${id}/assign`,
    data: { handler_id: handlerId },
  });
}
