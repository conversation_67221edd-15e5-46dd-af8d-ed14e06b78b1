import 'nprogress/nprogress.css';

import NProgress from 'nprogress';

import router from '@/router';
import { useUserStore } from '@/store';

NProgress.configure({ showSpinner: false });

router.beforeEach(async (to, from, next) => {
  NProgress.start();

  const userStore = useUserStore();

  if (userStore.token) {
    // 如果有token，验证token有效性
    const isValidToken = await userStore.checkToken();

    if (isValidToken) {
      // token有效，检查是否已获取用户信息
      if (!userStore.userInfo.name) {
        try {
          await userStore.getUserInfo();
        } catch (error) {
          console.error('Failed to get user info:', error);
          // 获取用户信息失败，跳转到登录页
          next({
            path: '/login',
            query: { redirect: encodeURIComponent(to.fullPath) },
          });
          return;
        }
      }
      next();
    } else {
      // token无效，跳转到登录页
      next({
        path: '/login',
        query: { redirect: encodeURIComponent(to.fullPath) },
      });
    }
  } else if (to.path === '/login') {
    next();
  } else {
    next({
      path: '/login',
      query: { redirect: encodeURIComponent(to.fullPath) },
    });
  }
});

router.afterEach((to) => {
  if (to.path === '/login') {
    const userStore = useUserStore();
    userStore.logout();
  }
  NProgress.done();
});
