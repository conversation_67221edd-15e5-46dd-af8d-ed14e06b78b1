import 'nprogress/nprogress.css';

import NProgress from 'nprogress';

import router from '@/router';
import { useUserStore } from '@/store';

NProgress.configure({ showSpinner: false });

router.beforeEach(async (to, from, next) => {
  // NProgress.start();

  const userStore = useUserStore();

  if (userStore.token) {
    next();
  } else if (to.path === '/login') {
    next();
  } else {
    next({
      path: '/login',
      query: { redirect: encodeURIComponent(to.fullPath) },
    });
  }
  // NProgress.done();
});

router.afterEach((to) => {
  if (to.path === '/login') {
    const userStore = useUserStore();
    userStore.logout();
  }
  // NProgress.done();
});
