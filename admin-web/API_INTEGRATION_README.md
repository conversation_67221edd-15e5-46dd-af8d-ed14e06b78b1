# 公安信访智能化系统前端 API 集成说明

## 概述

本文档说明了前端代码如何与后端 API 进行集成，包括 API 调用、认证、数据模型等。

## 主要更新内容

### 1. API 配置更新

- **请求基础配置**: 更新了 `src/utils/request/index.ts` 中的 API 前缀为 `/api/v1`
- **环境配置**: 更新了 `.env` 和 `.env.development` 文件中的 API URL 配置
- **代理配置**: 更新了 `vite.config.ts` 中的代理配置，指向后端服务器 `http://0.0.0.0:8000`

### 2. 新增 API 模块

#### 认证 API (`src/api/auth.ts`)
- `login()` - 用户登录
- `getCurrentUserInfo()` - 获取当前用户信息
- `testToken()` - 验证 token 有效性
- `getUsers()` - 获取用户列表（管理员）
- `createUser()` - 创建用户（管理员）
- `updateUserById()` - 更新用户信息（管理员）
- `deleteUserById()` - 删除用户（管理员）

#### 信件处理 API (`src/api/letter.ts`)
- `getLetterPetitions()` - 获取信件列表
- `getLetterPetitionDetail()` - 获取信件详情
- `createLetterPetition()` - 创建信件信访
- `updateLetterPetition()` - 更新信件信息
- `updateLetterPetitionStatus()` - 更新信件状态
- `assignLetterPetitionHandler()` - 分配信件处理人

#### 来访接待 API (`src/api/visiting.ts`)
- `getVisitingPetitions()` - 获取来访列表
- `getVisitingPetitionDetail()` - 获取来访详情
- `createVisitingPetition()` - 创建来访接待
- `updateVisitingPetition()` - 更新来访信息
- `updateVisitingPetitionStatus()` - 更新来访状态
- `assignVisitingPetitionHandler()` - 分配来访处理人

#### 在线信访 API (`src/api/online.ts`)
- `getOnlinePetitions()` - 获取在线信访列表
- `getOnlinePetitionDetail()` - 获取在线信访详情
- `createOnlinePetition()` - 创建在线信访
- `updateOnlinePetition()` - 更新在线信访信息
- `updateOnlinePetitionStatus()` - 更新在线信访状态
- `assignOnlinePetitionHandler()` - 分配在线信访处理人

#### 统计分析 API (`src/api/statistics.ts`)
- `getList()` - 获取基础列表数据
- `getCardList()` - 获取卡片列表数据
- `getDailyUpdates()` - 获取每日更新列表
- `getTodayUpdate()` - 获取今日更新数据
- `getDataAnalysis()` - 获取数据分析
- `getProjectList()` - 获取项目列表数据
- `getPurchaseList()` - 获取购买列表数据

#### 智能搜索 API (`src/api/search.ts`)
- `searchPetitions()` - 智能搜索信访记录
- `getSearchSuggestions()` - 获取搜索建议

#### 通用信访管理 API (`src/api/petitions.ts`)
- `getPetitions()` - 获取信访记录列表
- `getPetition()` - 获取信访记录详情
- `updatePetitionStatus()` - 更新信访记录状态
- `assignHandler()` - 分配信访记录处理人

#### 菜单管理 API (`src/api/menu.ts`)
- `getMenuListI18n()` - 获取国际化菜单列表

### 3. 数据模型更新

更新了 `src/api/model/petitionModel.ts`，包含：
- `Petition` - 信访记录主要接口
- `PetitionCreate` - 创建信访数据接口
- `PetitionUpdate` - 更新信访数据接口
- `LetterInfo`, `VisitingInfo`, `OnlineInfo` - 各类型特定信息接口
- `ProcessingRecord` - 处理记录接口
- `PetitionList` - 信访列表响应接口

### 4. 用户认证更新

更新了 `src/store/modules/user.ts`：
- 集成真实的登录 API
- 添加 token 验证功能
- 自动获取用户信息
- 错误处理和状态管理

### 5. 权限控制更新

更新了 `src/permission.ts`：
- 添加 token 有效性验证
- 自动获取用户信息
- 改进错误处理和重定向逻辑

### 6. 页面组件更新

#### 登录页面 (`src/pages/login/index.vue`)
- 集成真实的登录 API
- 添加错误处理和用户反馈
- 支持登录后重定向

#### 信件处理页面 (`src/pages/letter-processing/index.vue`)
- 替换静态数据为真实 API 调用
- 添加搜索和筛选功能
- 支持分页和加载状态
- 改进表格列定义和数据显示

### 7. API 测试页面

新增 `src/pages/api-test.vue`：
- 提供 API 测试界面
- 可以测试各个 API 端点
- 显示请求和响应日志
- 访问路径：`/api-test`

## 使用说明

### 1. 启动后端服务

确保后端 API 服务运行在 `http://0.0.0.0:8000`

### 2. 启动前端开发服务器

```bash
cd admin-web
npm install
npm run dev
```

前端服务将运行在 `http://localhost:3002`

### 3. 测试 API 连接

访问 `http://localhost:3002/api-test` 来测试 API 连接是否正常。

### 4. 登录系统

使用以下默认凭据登录：
- 用户名：`admin`
- 密码：`admin123`

### 5. 功能验证

登录后可以访问各个功能模块：
- 信件处理：`/letter-processing`
- 来访接待：`/visiting-reception`
- 在线信访：`/online-petition`
- 每日动态：`/daily-updates`

## 注意事项

1. **CORS 配置**: 确保后端服务器配置了正确的 CORS 设置
2. **Token 管理**: 系统会自动管理 JWT token，无需手动处理
3. **错误处理**: 所有 API 调用都包含错误处理和用户提示
4. **数据格式**: 确保前后端数据格式一致
5. **环境配置**: 根据部署环境调整 `.env` 文件中的 API URL

## 开发建议

1. 在开发新功能时，先在 API 测试页面验证 API 调用
2. 遵循现有的错误处理模式
3. 使用 TypeScript 类型定义确保类型安全
4. 添加适当的加载状态和用户反馈

## 故障排除

如果遇到 API 调用问题：

1. 检查后端服务是否正常运行
2. 验证 API URL 配置是否正确
3. 查看浏览器开发者工具的网络面板
4. 检查 token 是否有效
5. 使用 API 测试页面进行调试
