<p style="display:flex; justify-content: center">

</p>
<p align="center">
  <a href="https://tdesign.tencent.com/starter/vue-next/#/dashboard/base" target="_blank">
    <img alt="TDesign Logo" width="200" src="https://tdesign.gtimg.com/starter/brand-logo.svg">
  </a>
</p>

<p align="center">
  <a href="https://nodejs.org/en/about/releases/"><img src="https://img.shields.io/node/v/vite.svg" alt="node compatibility"></a>
  <a href="https://github.com/Tencent/tdesign-vue-next/blob/develop/LICENSE">
    <img src="https://img.shields.io/npm/l/tdesign-vue-next.svg?sanitize=true" alt="License">
  </a>
</p>

English | [简体中文](./README-zh_CN.md) 
### 项目介绍

智能公共安全信访系统（Intelligent Public Security Petition System）是基于 TDesign 开发的信访管理后台系统，使用 `Vue 3`、`Vite`、`Pinia` 和 `TypeScript` 构建。本系统提供了一套完整的信访业务管理功能，包括来访接待、信件处理、日常更新管理等模块，旨在提升信访工作的智能化和信息化水平。

<p>
  <a href="http://tdesign.tencent.com/starter/vue-next/">在线预览</a>
  ·
  <a href="https://tdesign.tencent.com/starter/">文档</a>
</p>

<img src="docs/starter.png">

### 系统功能

- **来访接待**：管理和记录访客接待流程，提高接待效率
- **信件处理**：高效处理各类信访信件，跟踪处理进度
- **日常更新管理**：系统日常数据维护和更新
- **首页数据分析**：提供关键数据可视化和统计分析
- **权限管理**：多级权限控制，保障系统安全

### 技术特点

- 基于 Vue 3 + TypeScript 开发的现代 Web 应用
- 使用 Vite 构建工具提升开发效率
- Pinia 状态管理，优化数据流转
- TDesign 组件库，提供丰富的 UI 组件
- 深色模式支持
- 自定义主题颜色
- 多种布局方案
- Mock 数据方案，方便开发调试

### 系统架构

项目采用前后端分离架构，前端基于 TDesign Vue Next Starter 脚手架开发，具有以下特点：

- **模块化设计**：按业务功能划分模块，便于维护和扩展
- **响应式布局**：适配不同尺寸的屏幕设备
- **API 统一管理**：集中管理所有后端接口调用
- **国际化支持**：支持多语言配置

### 开发指南

```bash
## 安装依赖
npm install

## 开发环境启动
npm run dev

## Mock 数据开发模式
npm run dev:mock
```

### 构建部署

```bash
## 构建生产环境
npm run build

## 构建测试环境
npm run build:test

## 预览构建结果
npm run preview
```


### 目录结构

```
admin-web/
├── src/                       # 源代码
│   ├── api/                   # API 接口定义
│   ├── assets/                # 静态资源文件
│   ├── components/            # 公共组件
│   ├── composables/           # 组合式 API
│   ├── config/                # 全局配置
│   ├── layouts/               # 布局组件
│   ├── pages/                 # 页面组件
│   │   ├── daily-updates/     # 日常更新模块
│   │   ├── homepage/          # 首页模块
│   │   ├── letter-processing/ # 信件处理模块
│   │   ├── login/             # 登录模块
│   │   └── visiting-reception/# 来访接待模块
│   ├── router/                # 路由配置
│   ├── store/                 # 状态管理
│   ├── style/                 # 样式文件
│   └── utils/                 # 工具函数
└── mock/                      # Mock 数据
```

### 代码规范

项目使用 ESLint 和 StyleLint 进行代码规范检查，遵循以下规范：

- [Angular 提交规范](https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-angular)
- [Vue 风格指南](https://v3.vuejs.org/style-guide/#rule-categories)

### Browser Support

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt="IE / Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br> IE / Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari |
| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Edge >=84                                                                                                                                                                                                        | Firefox >=83                                                                                                                                                                                                      | Chrome >=84                                                                                                                                                                                                   | Safari >=14.1                                                                                                                                                                                                 |

### Community Versions

There are kinds of community versions of starter-kit based on TDesign Vue Next, visit [community-link](https://tdesign.tencent.com/starter/docs/vue-next/community-link) for more detail. If you developed a community versions of tdesign starter, please create a issue or submit a pull request to let us know 😊.

### License

The MIT License. Please see [the license file](LICENSE) for more information.
