#!/usr/bin/env python3
"""
快速启动中国信访系统API服务
"""
import os
import subprocess
import sys

# 切换到API服务器目录
api_dir = "/Users/<USER>/Desktop/code/xinfang/api-server"
os.chdir(api_dir)

print("🚀 启动中国信访系统API服务...")
print("=" * 50)

# 检查main.py是否存在
if not os.path.exists("main.py"):
    print("❌ main.py文件不存在")
    sys.exit(1)

print("📍 工作目录:", os.getcwd())
print("🐍 Python版本:", sys.version)

# 尝试直接运行（如果已经在虚拟环境中）
try:
    print("🌟 正在启动服务...")
    print("📄 API文档将在以下地址提供:")
    print("   - Swagger UI: http://localhost:8000/docs")
    print("   - ReDoc: http://localhost:8000/redoc") 
    print("   - API基础: http://localhost:8000/api/v1")
    print("=" * 50)
    
    # 直接运行main.py
    subprocess.run([sys.executable, "main.py"])
    
except KeyboardInterrupt:
    print("\n🛑 服务已停止")
except Exception as e:
    print(f"❌ 启动失败: {e}")
    print("💡 请尝试手动启动:")
    print("   cd /Users/<USER>/Desktop/code/xinfang/api-server")
    print("   source venv/bin/activate") 
    print("   python main.py")
