#!/usr/bin/env python3
import requests
import subprocess
import sys
import time

def check_service():
    try:
        response = requests.get("http://localhost:8000/api/v1/", timeout=5)
        print("✅ 后端服务正在运行")
        return True
    except:
        print("❌ 后端服务未运行")
        return False

def start_backend():
    try:
        print("🚀 启动后端服务...")
        import os
        os.chdir("/Users/<USER>/Desktop/code/xinfang/api-server")
        
        # 激活虚拟环境并启动服务
        cmd = "source venv/bin/activate && python main.py"
        subprocess.Popen(cmd, shell=True)
        print("⏳ 等待服务启动...")
        time.sleep(5)
        
        if check_service():
            print("✅ 后端服务启动成功")
            return True
        else:
            print("❌ 后端服务启动失败")
            return False
    except Exception as e:
        print(f"❌ 启动服务时出错: {e}")
        return False

if __name__ == "__main__":
    print("🔍 检查后端服务状态...")
    
    if not check_service():
        if start_backend():
            print("✅ 服务已启动，可以运行测试")
        else:
            print("❌ 无法启动服务")
            sys.exit(1)
    
    print("🚀 运行前端API测试...")
    try:
        exec(open("/Users/<USER>/Desktop/code/xinfang/complete_frontend_api_test.py").read())
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
